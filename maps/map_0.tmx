<?xml version="1.0" encoding="UTF-8"?>
<map version="1.10" tiledversion="1.11.2" orientation="orthogonal" renderorder="right-down" width="16" height="9" tilewidth="64" tileheight="64" infinite="0" nextlayerid="7" nextobjectid="42">
 <tileset firstgid="1" name="grass" tilewidth="64" tileheight="64" tilecount="1" columns="1">
  <image source="grass.png" width="64" height="64"/>
 </tileset>
 <tileset firstgid="2" source="map.tsx"/>
 <layer id="3" name="Tlo" width="16" height="9">
  <data encoding="csv">
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
</data>
 </layer>
 <layer id="4" name="droga" width="16" height="9">
  <data encoding="csv">
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
0,10,4,4,4,11,0,10,4,4,4,4,4,4,11,0,
0,5,0,0,0,5,0,13,4,4,4,4,11,0,5,0,
0,5,0,0,0,5,0,0,0,0,0,0,5,0,5,0,
0,5,0,0,0,5,0,0,0,0,0,0,5,0,5,0,
0,5,10,4,4,12,0,0,0,0,0,0,5,0,5,0,
0,5,13,4,4,4,4,4,4,4,4,4,12,0,5,0,
0,6,0,0,0,0,9,4,4,4,4,4,4,4,12,0,
0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0
</data>
 </layer>
 <objectgroup id="6" name="RacingLine">
  <object id="41" name="Centerline" x="0" y="0">
   <polyline points="96,480 96.03,467.4 96.03,454.78 96.03,442.14 96.02,429.5 96,416.86 95.98,404.21 95.97,391.57 95.97,378.94 95.97,366.31 96,353.71 96.04,341.12 96.08,328.53 96.1,315.93 96.09,303.29 96.02,290.59 95.88,277.82 95.72,265.03 95.62,252.3 95.65,239.7 95.89,227.31 96.39,215.19 96.99,203.17 97.4,190.96 97.34,178.26 96.51,164.79 94.69,150.33 92.44,135.42 90.82,121.16 90.91,108.63 93.79,98.9 100.45,93 110.74,90.76 123.65,91.1 138.1,92.92 153.04,95.13 167.45,96.66 180.91,97.17 193.62,97 205.79,96.51 217.64,96.08 229.37,96.06 241.19,96.46 253.33,96.96 265.98,97.17 279.38,96.74 293.7,95.31 308.65,93.13 323.2,91.24 336.28,90.73 346.83,92.7 353.79,98.23 356.91,107.62 357.16,119.92 355.63,134.07 353.43,148.99 351.64,163.64 350.89,177.35 350.92,190.25 351.36,202.55 351.83,214.45 351.99,226.18 351.65,237.95 351.14,249.97 350.81,262.46 351.04,275.64 352.2,289.73 354.31,304.64 356.43,319.44 357.44,333.04 356.22,344.36 351.67,352.31 343.1,356.24 331.32,357 317.5,355.77 302.75,353.72 288.24,352.02 274.83,351.55 262.32,351.99 250.2,352.66 237.97,352.93 225.13,352.14 211.35,349.89 197.21,347.19 183.57,345.49 171.3,346.23 161.28,350.86 154.27,360.46 150.5,373.76 149.92,388.58 152.53,402.76 158.3,414.1 167.16,420.71 178.54,422.9 191.66,422.04 205.73,419.51 219.98,416.68 233.67,414.82 246.67,414.16 259.19,414.35 271.44,415.01 283.64,415.77 295.97,416.29 308.48,416.49 321.13,416.45 333.86,416.28 346.61,416.07 359.32,415.93 371.98,415.87 384.61,415.88 397.21,415.92 409.81,415.98 422.41,416.02 435.03,416.03 447.65,416.03 460.29,416.01 472.93,416 485.57,416 498.22,416.01 510.85,416.02 523.48,416.02 536.1,416.01 548.69,415.99 561.28,415.95 573.87,415.91 586.49,415.9 599.16,415.94 611.89,416.04 624.68,416.2 637.45,416.34 650.13,416.39 662.63,416.26 674.88,415.88 686.91,415.3 699,414.76 711.42,414.55 724.47,414.97 738.44,416.3 753.26,418.47 767.98,420.53 781.5,421.41 792.74,420.04 800.62,415.34 804.53,406.71 805.32,394.95 804.09,381.13 801.93,366.29 799.95,351.51 798.97,337.6 798.86,324.55 799.23,312.14 799.74,300.16 800,288.4 799.77,276.64 799.27,264.69 798.87,252.31 798.94,239.31 799.85,225.46 801.78,210.72 803.96,195.85 805.3,181.92 804.68,169.99 801,161.12 793.38,156.17 782.33,154.62 768.92,155.39 754.26,157.41 739.43,159.58 725.41,160.93 712.34,161.35 699.9,161.14 687.8,160.62 675.73,160.11 663.4,159.88 650.79,159.95 638.04,160.13 625.28,160.25 612.65,160.14 600.26,159.64 588.04,158.91 575.74,158.34 563.15,158.31 550.02,159.21 536.15,161.37 521.87,164.24 507.97,166.44 495.24,166.62 484.47,163.39 476.44,155.45 471.57,143.17 469.85,128.64 471.3,114.01 475.91,101.46 483.67,93.08 494.22,89.49 506.81,89.42 520.65,91.5 534.93,94.37 548.88,96.67 562.11,97.72 574.78,97.79 587.1,97.26 599.29,96.49 611.56,95.85 624.02,95.54 636.64,95.49 649.35,95.62 662.09,95.84 674.79,96.04 687.43,96.17 700.03,96.22 712.62,96.2 725.25,96.12 737.94,95.97 750.71,95.79 763.49,95.64 776.19,95.58 788.73,95.69 801.03,96.04 813.09,96.62 825.15,97.19 837.5,97.46 850.44,97.15 864.24,95.97 878.97,93.87 893.76,91.71 907.53,90.57 919.19,91.54 927.67,95.68 932.2,103.78 933.44,115.17 932.46,128.77 930.36,143.5 928.21,158.29 926.93,172.21 926.56,185.24 926.8,197.64 927.35,209.72 927.92,221.74 928.24,233.97 928.31,246.44 928.23,259.09 928.1,271.87 928.01,284.73 928.04,297.6 928.17,310.42 928.29,323.15 928.3,335.72 928.12,348.07 927.65,360.18 927.06,372.19 926.63,384.4 926.65,397.07 927.43,410.5 929.2,424.92 931.46,439.81 933.15,454.13 933.17,466.77 930.45,476.68 923.97,482.82 913.79,485.26 900.96,485.06 886.55,483.29 871.66,481.02 857.33,479.32 843.99,478.61 831.37,478.62 819.2,479.08 807.18,479.69 795.02,480.16 782.6,480.37 769.98,480.37 757.23,480.26 744.44,480.09 731.68,479.96 718.99,479.9 706.36,479.9 693.76,479.93 681.18,479.97 668.58,480.01 655.97,480.03 643.35,480.03 630.71,480.02 618.07,480.01 605.43,480 592.8,479.99 580.17,479.99 567.54,479.99 554.91,480 542.29,480 529.66,480 517.03,480 504.4,480 491.77,480 479.14,480 466.51,480 453.88,480 441.26,480 428.63,480 416,480"/>
  </object>
 </objectgroup>
</map>
