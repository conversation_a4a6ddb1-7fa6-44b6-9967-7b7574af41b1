import pygame as pg
from src.constants import *
from src.ui.widgets import Button
from src.core.assets import get_car_sprite
from src.core.game_data import (
    get_car_display_name,
    get_car_size_class,
    get_car_price,
    get_part_display_name,
    get_part_size_class,
    get_part_price,
    get_all_cars,
    get_all_parts,
    is_part_compatible
)


class ShopMenu:
    """
    Shop interface with two tabs: PARTS SHOP and CAR DEALERSHIP.
    Allows players to purchase parts and cars using in-game money.
    """

    TAB_PARTS = 'PARTS'
    TAB_CARS = 'CARS'

    PART_TYPES = ['engine', 'breaks', 'boost']
    SIZE_CLASSES = ['XS', 'S', 'M']

    def __init__(self, app, player, return_callback):
        self.app = app
        self.player = player
        self.return_callback = return_callback
        self.screen = app.screen

        # Fonts
        self.title_font = pg.font.SysFont('Consolas', 50, bold=True)
        self.info_font = pg.font.SysFont('Consolas', 24)
        self.small_font = pg.font.SysFont('Consolas', 18)
        self.price_font = pg.font.SysFont('Consolas', 16, bold=True)

        # UI state
        self.current_tab = self.TAB_PARTS
        self.selected_part_type = 'engine'
        self.selected_size_class = 'XS'

        # Clickable areas for purchases
        self.item_slots = []  # List of (pg.Rect, item_id, item_type)

        # Buttons
        self.buttons = []
        self.tab_buttons = []

        # Feedback message system
        self.feedback_message = ""
        self.feedback_color = TEXT_MAIN
        self.feedback_timer = 0

        self.init_ui()

    def init_ui(self):
        """Initialize buttons and UI elements."""
        self.buttons = []
        self.tab_buttons = []
        cx = self.screen.get_width() // 2
        h = self.screen.get_height()

        # Back button
        self.buttons.append(Button(
            self.app.lang.get("menu_back"),
            (cx, h - 50),
            self._on_back,
            app=self.app,
            custom_color=ACCENT_RED
        ))

        # Tab buttons
        tab_y = 130
        self.tab_buttons.append(Button(
            self.app.lang.get("shop_tab_parts"),
            (cx - 200, tab_y),
            lambda: self._switch_tab(self.TAB_PARTS),
            app=self.app
        ))
        self.tab_buttons.append(Button(
            self.app.lang.get("shop_tab_cars"),
            (cx + 200, tab_y),
            lambda: self._switch_tab(self.TAB_CARS),
            app=self.app
        ))

    def _on_back(self):
        self.app.audio.play_sfx('ui_back')
        self.return_callback()

    def _switch_tab(self, tab):
        if self.current_tab != tab:
            self.current_tab = tab
            self.app.audio.play_sfx('ui_select')

    def _show_feedback(self, message, color=TEXT_MAIN):
        self.feedback_message = message
        self.feedback_color = color
        self.feedback_timer = 120  # ~2 seconds at 60fps

    def _get_car_id_from_garage_item(self, item):
        """Extract car ID string from garage item (may be dict or string)."""
        if isinstance(item, dict):
            return item.get('model_id') or item.get('name')
        return item

    def _player_owns_car(self, car_id):
        """Check if player already owns a car."""
        for item in self.player.garage:
            owned_id = self._get_car_id_from_garage_item(item)
            if owned_id == car_id:
                return True
        return False

    def _purchase_car(self, car_id):
        """Attempt to purchase a car."""
        if self._player_owns_car(car_id):
            self._show_feedback(self.app.lang.get("shop_already_owned"), ACCENT_RED)
            self.app.audio.play_sfx('ui_error')
            return False

        price = get_car_price(car_id)
        if self.player.money < price:
            self._show_feedback(self.app.lang.get("shop_insufficient_funds"), ACCENT_RED)
            self.app.audio.play_sfx('ui_error')
            return False

        # Deduct money and add car to garage
        self.player.money -= price
        self.player.garage.append({
            'model_id': car_id,
            'mounted_parts': {}
        })

        self._show_feedback(self.app.lang.get("shop_purchase_success"), ACCENT_GREEN)
        self.app.audio.play_sfx('ui_select')
        return True

    def _purchase_part(self, part_id, part_type):
        """Attempt to purchase a part."""
        price = get_part_price(part_id)
        if self.player.money < price:
            self._show_feedback(self.app.lang.get("shop_insufficient_funds"), ACCENT_RED)
            self.app.audio.play_sfx('ui_error')
            return False

        # Deduct money and add part to inventory
        self.player.money -= price
        inv_key = {'engine': 'engines', 'breaks': 'breaks', 'boost': 'boosts'}[part_type]
        if inv_key not in self.player.inventory:
            self.player.inventory[inv_key] = []
        self.player.inventory[inv_key].append(part_id)

        self._show_feedback(self.app.lang.get("shop_purchase_success"), ACCENT_GREEN)
        self.app.audio.play_sfx('ui_select')
        return True

    def update(self, event):
        """Handle input events."""
        for btn in self.buttons:
            btn.handle_event(event)
        for btn in self.tab_buttons:
            btn.handle_event(event)

        if event.type == pg.MOUSEBUTTONDOWN and event.button == 1:
            self._handle_click(event.pos)

    def _handle_click(self, mouse_pos):
        """Handle clicks on shop items."""
        for rect, item_id, item_type in self.item_slots:
            if rect.collidepoint(mouse_pos):
                if item_type == 'car':
                    self._purchase_car(item_id)
                else:
                    self._purchase_part(item_id, item_type)
                return True
        return False

    def draw(self, screen):
        """Main draw method."""
        screen.fill(BG_COLOR)
        self.item_slots = []

        # Update feedback timer
        if self.feedback_timer > 0:
            self.feedback_timer -= 1
            if self.feedback_timer <= 0:
                self.feedback_message = ""

        # Title with shadow
        title_text = self.app.lang.get("shop_title")
        # Shadow
        shadow_surf = self.title_font.render(title_text, True, (15, 15, 20))
        shadow_rect = shadow_surf.get_rect(center=(screen.get_width() // 2 + 3, 53))
        screen.blit(shadow_surf, shadow_rect)
        # Main title
        title_surf = self.title_font.render(title_text, True, ACCENT_GOLD)
        title_rect = title_surf.get_rect(center=(screen.get_width() // 2, 50))
        screen.blit(title_surf, title_rect)

        # Money display with background
        money_text = f"{self.app.lang.get('shop_your_money')}: ${self.player.money:,}"
        money_surf = self.info_font.render(money_text, True, ACCENT_GOLD)
        money_rect = money_surf.get_rect(topright=(screen.get_width() - 50, 25))
        # Background panel for money
        bg_rect = money_rect.inflate(20, 10)
        pg.draw.rect(screen, PANEL_BG, bg_rect, border_radius=5)
        pg.draw.rect(screen, ACCENT_GOLD, bg_rect, 2, border_radius=5)
        screen.blit(money_surf, money_rect)

        # Tab buttons with active indicator
        for i, btn in enumerate(self.tab_buttons):
            btn.draw(screen)
            is_active = (i == 0 and self.current_tab == self.TAB_PARTS) or \
                       (i == 1 and self.current_tab == self.TAB_CARS)
            if is_active:
                # Underline for active tab
                underline_rect = pg.Rect(btn.rect.left, btn.rect.bottom + 2, btn.rect.width, 3)
                pg.draw.rect(screen, ACCENT_GOLD, underline_rect)

        # Draw current tab content
        if self.current_tab == self.TAB_PARTS:
            self._draw_parts_tab(screen)
        else:
            self._draw_cars_tab(screen)

        # Feedback message
        if self.feedback_message:
            fb_surf = self.info_font.render(self.feedback_message, True, self.feedback_color)
            fb_rect = fb_surf.get_rect(center=(screen.get_width() // 2, screen.get_height() - 100))
            screen.blit(fb_surf, fb_rect)

        # Buttons
        for btn in self.buttons:
            btn.draw(screen)

    def _draw_parts_tab(self, screen):
        """Draw the parts shop tab."""
        mouse_pos = pg.mouse.get_pos()

        # Left panel: Part type and size class selectors
        left_panel_x = 50
        left_panel_y = 180
        left_panel_w = 220
        left_panel_h = 420

        pg.draw.rect(screen, PANEL_BG, (left_panel_x, left_panel_y, left_panel_w, left_panel_h), border_radius=10)
        pg.draw.rect(screen, TEXT_DIM, (left_panel_x, left_panel_y, left_panel_w, left_panel_h), 2, border_radius=10)

        # Part type selector
        type_title = self.app.lang.get("parts_select_type")
        type_surf = self.small_font.render(type_title, True, TEXT_MAIN)
        screen.blit(type_surf, (left_panel_x + 10, left_panel_y + 15))

        part_labels = {
            'engine': self.app.lang.get("parts_engine"),
            'breaks': self.app.lang.get("parts_brakes"),
            'boost': self.app.lang.get("parts_boost")
        }

        btn_y = left_panel_y + 50
        for ptype in self.PART_TYPES:
            is_active = ptype == self.selected_part_type
            btn_rect = pg.Rect(left_panel_x + 10, btn_y, left_panel_w - 20, 38)

            bg_color = ACCENT_BLUE if is_active else PANEL_BG
            if btn_rect.collidepoint(mouse_pos):
                bg_color = BUTTON_HOVER_COLOR

            pg.draw.rect(screen, bg_color, btn_rect, border_radius=5)
            pg.draw.rect(screen, TEXT_DIM, btn_rect, 1, border_radius=5)

            label_surf = self.small_font.render(part_labels[ptype], True, TEXT_MAIN if is_active else TEXT_DIM)
            label_rect = label_surf.get_rect(center=btn_rect.center)
            screen.blit(label_surf, label_rect)

            if btn_rect.collidepoint(mouse_pos) and pg.mouse.get_pressed()[0]:
                if self.selected_part_type != ptype:
                    self.selected_part_type = ptype
                    self.app.audio.play_sfx('ui_hover')

            btn_y += 50

        # Size class selector
        size_title = self.app.lang.get("shop_select_size")
        size_surf = self.small_font.render(size_title, True, TEXT_MAIN)
        screen.blit(size_surf, (left_panel_x + 10, btn_y + 25))

        size_labels = {
            'XS': self.app.lang.get("shop_size_xs"),
            'S': self.app.lang.get("shop_size_s"),
            'M': self.app.lang.get("shop_size_m")
        }

        btn_y += 55
        for size in self.SIZE_CLASSES:
            is_active = size == self.selected_size_class
            btn_rect = pg.Rect(left_panel_x + 10, btn_y, left_panel_w - 20, 38)

            bg_color = ACCENT_BLUE if is_active else PANEL_BG
            if btn_rect.collidepoint(mouse_pos):
                bg_color = BUTTON_HOVER_COLOR

            pg.draw.rect(screen, bg_color, btn_rect, border_radius=5)
            pg.draw.rect(screen, TEXT_DIM, btn_rect, 1, border_radius=5)

            # Shortened label - just size class without long description
            label_text = f"{size}"
            label_surf = self.small_font.render(label_text, True, TEXT_MAIN if is_active else TEXT_DIM)
            label_rect = label_surf.get_rect(center=btn_rect.center)
            screen.blit(label_surf, label_rect)
            
            # Show full name below in smaller text if active
            if is_active:
                desc_surf = pg.font.SysFont('Consolas', 14).render(size_labels[size], True, TEXT_DIM)
                desc_rect = desc_surf.get_rect(center=(btn_rect.centerx, btn_rect.bottom - 10))
                screen.blit(desc_surf, desc_rect)

            if btn_rect.collidepoint(mouse_pos) and pg.mouse.get_pressed()[0]:
                if self.selected_size_class != size:
                    self.selected_size_class = size
                    self.app.audio.play_sfx('ui_hover')

            btn_y += 50

        # Right panel: Available parts grid
        self._draw_parts_grid(screen, mouse_pos)

    def _draw_parts_grid(self, screen, mouse_pos):
        """Draw the grid of available parts for purchase."""
        # Determine inventory key based on part type
        inv_map = {'engine': 'engines', 'breaks': 'breaks', 'boost': 'boosts'}
        inv_key = inv_map[self.selected_part_type]

        # Get all parts of selected type and size
        all_parts = get_all_parts(inv_key)
        filtered_parts = [p for p in all_parts if get_part_size_class(p) == self.selected_size_class]

        # Right panel dimensions
        right_panel_x = 300
        right_panel_y = 180
        right_panel_w = screen.get_width() - right_panel_x - 50
        right_panel_h = 420

        pg.draw.rect(screen, PANEL_BG, (right_panel_x, right_panel_y, right_panel_w, right_panel_h), border_radius=10)
        pg.draw.rect(screen, TEXT_DIM, (right_panel_x, right_panel_y, right_panel_w, right_panel_h), 2, border_radius=10)

        # Title
        part_labels = {
            'engine': self.app.lang.get("parts_engine"),
            'breaks': self.app.lang.get("parts_brakes"),
            'boost': self.app.lang.get("parts_boost")
        }
        title = f"{part_labels[self.selected_part_type]} - {self.selected_size_class}"
        title_surf = self.info_font.render(title, True, TEXT_MAIN)
        screen.blit(title_surf, (right_panel_x + 15, right_panel_y + 10))

        if not filtered_parts:
            no_parts = self.app.lang.get("shop_no_parts_available")
            no_surf = self.info_font.render(no_parts, True, TEXT_DIM)
            no_rect = no_surf.get_rect(center=(right_panel_x + right_panel_w // 2, right_panel_y + right_panel_h // 2))
            screen.blit(no_surf, no_rect)
            return

        # Draw parts in a grid
        slot_w, slot_h = 280, 70
        cols = max(1, (right_panel_w - 30) // (slot_w + 10))
        start_x = right_panel_x + 15
        start_y = right_panel_y + 50

        # Check compatibility with current car
        current_car = self.player.current_car

        for i, part_id in enumerate(filtered_parts):
            row = i // cols
            col = i % cols

            slot_x = start_x + col * (slot_w + 10)
            slot_y = start_y + row * (slot_h + 10)

            if slot_y + slot_h > right_panel_y + right_panel_h - 10:
                break  # Don't draw outside panel

            slot_rect = pg.Rect(slot_x, slot_y, slot_w, slot_h)
            is_hovered = slot_rect.collidepoint(mouse_pos)

            # Background
            bg_color = BUTTON_HOVER_COLOR if is_hovered else (60, 60, 70)
            pg.draw.rect(screen, bg_color, slot_rect, border_radius=8)
            pg.draw.rect(screen, TEXT_DIM, slot_rect, 2, border_radius=8)

            # Part name
            display_name = get_part_display_name(part_id, self.app.lang)
            name_surf = self.small_font.render(display_name, True, TEXT_MAIN)
            screen.blit(name_surf, (slot_x + 10, slot_y + 8))

            # Price
            price = get_part_price(part_id)
            price_text = f"${price:,}"
            can_afford = self.player.money >= price
            price_color = ACCENT_GREEN if can_afford else ACCENT_RED
            price_surf = self.price_font.render(price_text, True, price_color)
            screen.blit(price_surf, (slot_x + 10, slot_y + 30))

            # Compatibility indicator
            if current_car:
                is_compatible = is_part_compatible(current_car, part_id)
                if is_compatible:
                    compat_text = f"✓ {self.app.lang.get('shop_compatible')}"
                    compat_color = ACCENT_GREEN
                else:
                    compat_text = f"✗ {self.app.lang.get('parts_incompatible_short')}"
                    compat_color = ACCENT_RED
                compat_surf = self.small_font.render(compat_text, True, compat_color)
                screen.blit(compat_surf, (slot_x + 10, slot_y + 48))

            # Buy button area
            buy_rect = pg.Rect(slot_x + slot_w - 70, slot_y + 20, 60, 30)
            buy_hover = buy_rect.collidepoint(mouse_pos)
            buy_color = ACCENT_GREEN if can_afford else (80, 80, 80)
            if buy_hover and can_afford:
                buy_color = (80, 220, 80)
            pg.draw.rect(screen, buy_color, buy_rect, border_radius=5)
            buy_text = self.app.lang.get("shop_buy")
            buy_surf = self.small_font.render(buy_text, True, TEXT_MAIN)
            buy_text_rect = buy_surf.get_rect(center=buy_rect.center)
            screen.blit(buy_surf, buy_text_rect)

            # Store for click detection
            self.item_slots.append((slot_rect, part_id, self.selected_part_type))

        # Legend
        legend_y = right_panel_y + right_panel_h - 25
        legend = self.app.lang.get("shop_click_to_buy")
        legend_surf = self.small_font.render(legend, True, TEXT_DIM)
        legend_rect = legend_surf.get_rect(midbottom=(right_panel_x + right_panel_w // 2, legend_y))
        screen.blit(legend_surf, legend_rect)

    def _draw_cars_tab(self, screen):
        """Draw the car dealership tab."""
        mouse_pos = pg.mouse.get_pos()

        # Get all available cars
        all_cars = get_all_cars()

        # Grid layout settings (improved spacing to prevent overlap)
        start_x = 80
        start_y = 180
        gap_x = 220
        gap_y = 230
        cols = 5

        for i, car_id in enumerate(all_cars):
            row = i // cols
            col = i % cols

            x = start_x + col * gap_x
            y = start_y + row * gap_y

            if y > screen.get_height() - 150:
                break  # Don't draw outside screen

            rect = pg.Rect(x, y, 200, 210)
            is_hovered = rect.collidepoint(mouse_pos)
            is_owned = self._player_owns_car(car_id)

            # Background color
            if is_owned:
                bg_color = (40, 60, 40)  # Greenish for owned
            elif is_hovered:
                bg_color = BUTTON_HOVER_COLOR
            else:
                bg_color = (60, 60, 70)

            pg.draw.rect(screen, bg_color, rect, border_radius=10)
            pg.draw.rect(screen, TEXT_DIM if not is_owned else ACCENT_GREEN, rect, 2, border_radius=10)

            # Car sprite
            sprite = get_car_sprite(self.app.assets, car_id)
            if sprite:
                scaled = pg.transform.scale(sprite, (75, 120))  # Maintain 5:8 aspect ratio
                sprite_rect = scaled.get_rect(center=(rect.centerx, rect.y + 65))
                screen.blit(scaled, sprite_rect)

            # Car name
            display_name = get_car_display_name(car_id, self.app.lang)
            name_surf = self.small_font.render(display_name, True, TEXT_MAIN)
            name_rect = name_surf.get_rect(midtop=(rect.centerx, rect.y + 115))
            screen.blit(name_surf, name_rect)

            # Size class
            size_class = get_car_size_class(car_id)
            class_surf = self.small_font.render(f"[{size_class}]", True, ACCENT_GOLD)
            class_rect = class_surf.get_rect(midtop=(rect.centerx, rect.y + 135))
            screen.blit(class_surf, class_rect)

            # Price or Owned indicator
            if is_owned:
                status_text = self.app.lang.get("shop_owned")
                status_color = ACCENT_GREEN
            else:
                price = get_car_price(car_id)
                status_text = f"${price:,}"
                can_afford = self.player.money >= price
                status_color = ACCENT_GREEN if can_afford else ACCENT_RED

            status_surf = self.price_font.render(status_text, True, status_color)
            status_rect = status_surf.get_rect(midtop=(rect.centerx, rect.y + 155))
            screen.blit(status_surf, status_rect)

            # Buy button (only if not owned)
            if not is_owned:
                buy_rect = pg.Rect(rect.x + 50, rect.y + 180, 100, 25)
                price = get_car_price(car_id)
                can_afford = self.player.money >= price
                buy_hover = buy_rect.collidepoint(mouse_pos)
                buy_color = ACCENT_GREEN if can_afford else (80, 80, 80)
                if buy_hover and can_afford:
                    buy_color = (80, 220, 80)

                pg.draw.rect(screen, buy_color, buy_rect, border_radius=5)
                buy_text = self.app.lang.get("shop_buy")
                buy_surf = self.small_font.render(buy_text, True, TEXT_MAIN)
                buy_text_rect = buy_surf.get_rect(center=buy_rect.center)
                screen.blit(buy_surf, buy_text_rect)

                # Store for click detection
                self.item_slots.append((rect, car_id, 'car'))
