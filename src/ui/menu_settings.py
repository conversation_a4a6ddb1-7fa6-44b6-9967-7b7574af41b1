import pygame as pg
from src.constants import *
from src.ui.widgets import <PERSON><PERSON>, InputBox

class GlobalSettingsMenu:
    def __init__(self, app, return_callback):
        self.app = app
        self.return_callback = return_callback
        self.title_font = pg.font.SysFont('Consolas', 50, bold=True)
        self.buttons = []
        self.current_state = 'MAIN'
        self.init_main_view()

    def _get_cx(self):
        return self.app.screen.get_width() // 2

    def init_main_view(self):
        self.current_state = 'MAIN'
        self.buttons = []
        cx = self._get_cx()
        y, gap = 250, 70
        
        opts = [
            (self.app.lang.get("settings_audio"), self.init_audio_view),
            (self.app.lang.get("settings_graphics"), self.init_graphics_view),
            (self.app.lang.get("settings_language"), self.init_language_view),
            (self.app.lang.get("menu_back"), self._on_back, ACCENT_RED)
        ]
        for i, (txt, act, *c) in enumerate(opts):
            self.buttons.append(Button(txt, (cx, y + i*gap), act, app=self.app, custom_color=c[0] if c else None))

    def _on_back(self):
        self.app.audio.play_sfx('ui_back')
        self.return_callback()

    # --- AUDIO SETTINGS ---
    def init_audio_view(self):
        self.current_state = 'AUDIO'
        self.buttons = []
        cx = self._get_cx()
        y, gap = 250, 70
        
        # Audio Logic
        vol_m = int(self.app.audio.music_volume * 100)
        vol_s = int(self.app.audio.sfx_volume * 100)
        
        self.buttons.append(Button(f"{self.app.lang.get('audio_music')}: {vol_m}%", (cx, y), self.cycle_music_vol, app=self.app))
        self.buttons.append(Button(f"{self.app.lang.get('audio_sfx')}: {vol_s}%", (cx, y+gap), self.cycle_sfx_vol, app=self.app))
        self.buttons.append(Button(self.app.lang.get("menu_back"), (cx, y+gap*2), self.init_main_view, app=self.app, custom_color=ACCENT_RED))

    def cycle_music_vol(self):
        curr = int(self.app.audio.music_volume * 100)
        nxt = (curr + 25) if curr < 100 else 0
        self.app.audio.set_music_volume(nxt)
        self.init_audio_view() # Refresh text

    def cycle_sfx_vol(self):
        curr = int(self.app.audio.sfx_volume * 100)
        nxt = (curr + 25) if curr < 100 else 0
        self.app.audio.set_sfx_volume(nxt)
        self.app.audio.play_sfx('ui_select') # Preview sound
        self.init_audio_view()

    # --- GRAPHICS SETTINGS (Keep existing logic, add sound to buttons) ---
    def init_graphics_view(self):
        self.current_state = 'GRAPHICS'
        self.buttons = []
        cx = self._get_cx()
        y, gap = 150, 60
        gs = self.app.global_settings

        res_idx = gs.get("resolution_idx", 0)
        curr_res = RESOLUTIONS[res_idx] if res_idx < len(RESOLUTIONS) else RESOLUTIONS[0]
        self.buttons.append(Button(f"{self.app.lang.get('gfx_resolution')}: {curr_res[0]}x{curr_res[1]}", (cx, y), self.cycle_res, app=self.app))

        fs_txt = self.app.lang.get("val_on") if gs.get("fullscreen") else self.app.lang.get("val_off")
        self.buttons.append(Button(f"{self.app.lang.get('gfx_fullscreen')}: {fs_txt}", (cx, y+gap), self.toggle_fs, app=self.app))

        # FPS Limit display - show "UNLIMITED" for 0
        max_fps = gs.get('max_fps', 60)
        fps_txt = self.app.lang.get("val_unlimited") if max_fps == 0 else str(max_fps)
        self.buttons.append(Button(f"{self.app.lang.get('gfx_fps')}: {fps_txt}", (cx, y+gap*2), self.cycle_fps, app=self.app))

        q = gs.get("quality", "HIGH")
        q_str = self.app.lang.get(f"quality_{q.lower()}")
        self.buttons.append(Button(f"{self.app.lang.get('gfx_quality')}: {q_str}", (cx, y+gap*3), self.cycle_qual, app=self.app))

        # FPS Display Toggle
        show_fps = gs.get("show_fps", False)
        fps_display_txt = self.app.lang.get("val_on") if show_fps else self.app.lang.get("val_off")
        self.buttons.append(Button(f"{self.app.lang.get('gfx_show_fps')}: {fps_display_txt}", (cx, y+gap*4), self.toggle_show_fps, app=self.app))

        self.buttons.append(Button(self.app.lang.get("menu_back"), (cx, y+gap*6), self.init_main_view, app=self.app, custom_color=ACCENT_RED))

    def _save_gfx(self):
        self.app.apply_graphics(self.app.global_settings)
        self.app.data_manager.save_global_settings(self.app.global_settings)
        self.init_graphics_view()

    def cycle_res(self):
        self.app.global_settings["resolution_idx"] = (self.app.global_settings.get("resolution_idx", 0) + 1) % len(RESOLUTIONS)
        self._save_gfx()
    def toggle_fs(self):
        self.app.global_settings["fullscreen"] = not self.app.global_settings.get("fullscreen")
        self._save_gfx()
    def cycle_fps(self):
        curr = self.app.global_settings.get("max_fps", 60)
        try:
            idx = FPS_LIMITS.index(curr)
        except ValueError:
            idx = 0  # Default to first option if current value not in list
        self.app.global_settings["max_fps"] = FPS_LIMITS[(idx + 1) % len(FPS_LIMITS)]
        self._save_gfx()
    def toggle_show_fps(self):
        self.app.global_settings["show_fps"] = not self.app.global_settings.get("show_fps", False)
        self._save_gfx()
    def cycle_qual(self):
        modes = ["LOW", "MED", "HIGH"]
        self.app.global_settings["quality"] = modes[(modes.index(self.app.global_settings.get("quality","HIGH"))+1)%len(modes)]
        self._save_gfx()

    # --- LANGUAGE SETTINGS ---
    def init_language_view(self):
        self.current_state = 'LANGUAGE'
        self.buttons = []
        cx = self._get_cx()
        y, gap = 200, 60
        langs = [("English", "en"), ("Polski", "pl"), ("Deutsch", "de"), ("Español", "es"), ("Français", "fr"), ("Português", "pt")]
        
        for i, (nm, code) in enumerate(langs):
            self.buttons.append(Button(nm, (cx, y + i*gap), lambda c=code: self.change_lang(c), app=self.app))
        self.buttons.append(Button(self.app.lang.get("menu_back"), (cx, y + len(langs)*gap + 20), self.init_main_view, app=self.app, custom_color=ACCENT_RED))

    def change_lang(self, code):
        self.app.lang.load_language(code)
        self.app.global_settings["language"] = code
        self.app.data_manager.save_global_settings(self.app.global_settings)
        self.init_language_view()

    def update(self, event):
        for btn in self.buttons: btn.handle_event(event)

    def draw(self, screen):
        screen.fill(BG_COLOR)
        t_map = {'MAIN':'settings_global_title','GRAPHICS':'settings_graphics','AUDIO':'settings_audio','LANGUAGE':'settings_language'}
        title_text = self.app.lang.get(t_map.get(self.current_state))
        cx = self._get_cx()
        
        # Title with shadow
        shadow_surf = self.title_font.render(title_text, True, (15, 15, 20))
        shadow_rect = shadow_surf.get_rect(center=(cx + 3, 83))
        screen.blit(shadow_surf, shadow_rect)
        
        t_surf = self.title_font.render(title_text, True, ACCENT_GOLD)
        t_rect = t_surf.get_rect(center=(cx, 80))
        screen.blit(t_surf, t_rect)
        
        for btn in self.buttons: btn.draw(screen)

class PlayerSettingsMenu:
    def __init__(self, app, player, return_cb):
        self.app = app
        self.player = player
        self.return_cb = return_cb
        self.buttons = []
        self.input_box = None
        self.font = pg.font.SysFont('Consolas', 50, bold=True)
        self.init_ui()

    def init_ui(self):
        cx = self.app.screen.get_width() // 2
        self.input_box = InputBox((cx, 250), self.player.name, app=self.app)
        self.buttons = [
            Button(self.app.lang.get("btn_save"), (cx, 350), self.save_name, app=self.app, custom_color=ACCENT_GREEN),
            Button(self.app.lang.get("menu_back"), (cx, 450), self._on_back, app=self.app, custom_color=ACCENT_RED)
        ]

    def _on_back(self):
        self.app.audio.play_sfx('ui_back')
        self.return_cb()

    def save_name(self):
        if self.input_box.text: self.player.set_name(self.input_box.text)
        self.return_cb()

    def update(self, event):
        self.input_box.handle_event(event)
        for b in self.buttons: b.handle_event(event)

    def draw(self, screen):
        screen.fill(BG_COLOR)
        cx = self.app.screen.get_width() // 2
        
        # Title with shadow
        title_text = self.app.lang.get("settings_player_title")
        shadow_surf = self.font.render(title_text, True, (15, 15, 20))
        shadow_rect = shadow_surf.get_rect(center=(cx + 3, 83))
        screen.blit(shadow_surf, shadow_rect)
        
        t_surf = self.font.render(title_text, True, ACCENT_GOLD)
        t_rect = t_surf.get_rect(center=(cx, 80))
        screen.blit(t_surf, t_rect)
        
        lbl = pg.font.SysFont('Consolas', 28).render(self.app.lang.get("label_enter_name"), True, TEXT_DIM)
        screen.blit(lbl, lbl.get_rect(center=(cx, 180)))
        
        self.input_box.draw(screen)
        for b in self.buttons: b.draw(screen)