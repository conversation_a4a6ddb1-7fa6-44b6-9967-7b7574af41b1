import os
import json
import shutil
# We import both paths: DATA_DIR (installation) and USER_DATA_DIR (system)
from src.constants import DATA_DIR, USER_DATA_DIR, SCREEN_WIDTH, SCREEN_HEIGHT, FU<PERSON>SCRE<PERSON>, RESOLUTIONS

class DataManager:
    def __init__(self):
        # --- SAVE PATHS (User Data) ---
        # Here we save active saves and settings (~/.local/share/CA-Racing)
        self.user_data_dir = USER_DATA_DIR
        self.saves_dir = os.path.join(self.user_data_dir, 'saves')
        self.global_settings_path = os.path.join(self.user_data_dir, 'settings.json')

        # --- READ PATHS (Game Data) ---
        self.game_data_path = os.path.join(DATA_DIR, 'game_data.json') # Example

        # We make sure the user's save folder exists
        if not os.path.exists(self.saves_dir):
            try:
                os.makedirs(self.saves_dir)
            except OSError as e:
                print(f"[DATA] Critical Error creating saves dir: {e}")

    def check_save_slots(self):
        """Returns a dict {1: {'exists': bool, 'name': str}, ...} checking if a save exists in the user's folder."""
        status = {}
        for i in range(1, 4):
            save_path = os.path.join(self.saves_dir, f'save_{i}', 'player_state.json')
            exists = os.path.exists(save_path)
            name = None
            if exists:
                try:
                    data = self._load_json(save_path)
                    name = data.get('player', {}).get('name', 'Unknown')
                except Exception:
                    name = "Corrupted"
            
            status[i] = {'exists': exists, 'name': name}
        return status

    def _get_default_player_state(self):
        """Generates a clean initial player state in the code."""
        return {
            "player": {
                "name": "Racer",
                "level": 1,
                "money": 500,
                "exp": 0
            },
            "garage": [
                {
                    "instance_id": "auto_001",
                    "model_id": "car_0",
                    "custom_name": "Starter Car",
                    "mounted_parts": {
                        "engine": "xs_0_engine",
                        "breaks": "xs_0_breaks",
                        "boost": "xs_0_boost"
                    }
                }
            ],
            "inventory": {
                "engines": [],
                "breaks": [],
                "boosts": []
            }
        }

    def create_new_save(self, slot_id):
        """Creates a new save in the given slot using default data."""
        target_dir = os.path.join(self.saves_dir, f'save_{slot_id}')
        if not os.path.exists(target_dir):
            os.makedirs(target_dir)
            
        default_data = self._get_default_player_state()
        dst_file = os.path.join(target_dir, 'player_state.json')
        
        print(f"[DATA] Creating new clean save in Slot {slot_id}")
        return self._save_json(dst_file, default_data)

    def reset_save_slot(self, slot_id):
        """Hard Reset: Overwrites an existing slot with default data."""
        print(f"[DATA] Performing Hard Reset on Slot {slot_id}...")
        return self.create_new_save(slot_id)

    def load_game_data(self):
        # We load game data from the installation folder
        return self._load_json(self.game_data_path)

    def load_player_state(self, slot_id):
        path = os.path.join(self.saves_dir, f'save_{slot_id}', 'player_state.json')
        return self._load_json(path)

    def save_player_state(self, slot_id, data):
        path = os.path.join(self.saves_dir, f'save_{slot_id}', 'player_state.json')
        return self._save_json(path, data)

    def load_global_settings(self):
        # Determine default resolution index based on detected SCREEN_WIDTH/HEIGHT
        default_res_idx = 0
        try:
            default_res_idx = RESOLUTIONS.index((SCREEN_WIDTH, SCREEN_HEIGHT))
        except ValueError:
            # Should not happen as we added native to RESOLUTIONS in constants.py
            pass

        default_settings = {
            "resolution_idx": default_res_idx,
            "fullscreen": FULLSCREEN,
            "max_fps": 60,
            "quality": "HIGH",
            "language": "en",
            "vol_music": 50,
            "vol_sfx": 50,
            "show_fps": False
        }
        
        # If there are no settings in the user's folder, create default ones
        if not os.path.exists(self.global_settings_path):
            self._save_json(self.global_settings_path, default_settings)
            return default_settings
        
        data = self._load_json(self.global_settings_path)
        # Fill in the missing keys
        for key, val in default_settings.items():
            if key not in data:
                data[key] = val
        return data

    def save_global_settings(self, data):
        return self._save_json(self.global_settings_path, data)

    def _load_json(self, path):
        try:
            with open(path, mode='r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"[DATA] Error loading {path}: {e}")
            return {}

    def _save_json(self, path, data):
        try:
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=4)
            return True
        except Exception as e:
            print(f"[DATA] Error saving {path}: {e}")
            return False