"""
Game Data Lookup Module
=======================
Provides functions to look up display names and attributes for cars and parts.
Uses internal IDs for save file compatibility while exposing localized display names.
"""
import json
import os
import logging

# Game data cache
_game_data = None


def _load_game_data():
    """Loads the game data from data/game_data.json."""
    global _game_data
    if _game_data is not None:
        return _game_data
    
    data_path = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'game_data.json')
    try:
        with open(data_path, 'r', encoding='utf-8') as f:
            _game_data = json.load(f)
    except Exception as e:
        print(f"[GAME_DATA] Error loading game_data.json: {e}")
        _game_data = {"cars": {}, "parts": {"engines": {}, "breaks": {}, "boosts": {}}, "tracks": {}}
    
    return _game_data


def normalize_track_id(track_input):
    """
    Konwertuje różne formaty track ID na standard "track_X".
    
    Obsługuje:
    - int: 0 → "track_0"
    - str cyfry: "0" → "track_0"  
    - str ID: "track_0" → "track_0" (bez zmian)
    
    Args:
        track_input: int, str indeksu, lub str ID
    
    Returns:
        str: Znormalizowane ID w formacie "track_X"
    
    Examples:
        >>> normalize_track_id(0)
        'track_0'
        >>> normalize_track_id("1")
        'track_1'
        >>> normalize_track_id("track_2")
        'track_2'
    """
    # Przypadek 1: Integer
    if isinstance(track_input, int):
        return f"track_{track_input}"
    
    # Przypadek 2: String
    if isinstance(track_input, str):
        # Jeśli to sama cyfra
        if track_input.isdigit():
            return f"track_{track_input}"
        # Jeśli już ma prawidłowy format
        if track_input.startswith("track_"):
            return track_input
    
    # Fallback dla nieprawidłowych wartości
    logging.warning(f"[GAME_DATA] Invalid track ID format: {track_input}, falling back to track_0")
    return "track_0"


def get_car_data(car_id):
    """
    Returns the full car data dict for a given car ID.
    Returns None if car not found.
    """
    data = _load_game_data()
    return data.get("cars", {}).get(car_id)


def get_car_display_name(car_id, lang_manager):
    """
    Returns the localized display name for a car.
    Falls back to car_id if not found.
    
    Args:
        car_id: Internal car ID (e.g., "car_0")
        lang_manager: LanguageManager instance for localization
    
    Returns:
        Localized display name string
    """
    car_data = get_car_data(car_id)
    if car_data and "name_key" in car_data:
        return lang_manager.get(car_data["name_key"])
    return car_id  # Fallback to internal ID


def get_car_size_class(car_id):
    """
    Returns the size class for a car from game data.
    Falls back to calculation if not in data.
    """
    car_data = get_car_data(car_id)
    if car_data and "size_class" in car_data:
        return car_data["size_class"]
    
    # Fallback: extract index and determine class
    if isinstance(car_id, dict):
        car_id = car_id.get('model_id') or car_id.get('name', '')
    
    try:
        parts = car_id.split('_')
        if len(parts) >= 2:
            index = int(parts[1])
            if index < 6:
                return 'XS'
            elif index < 12:
                return 'S'
            elif index < 18:
                return 'M'
    except (ValueError, IndexError, AttributeError):
        pass
    
    return 'XS'  # Default fallback


def get_part_data(part_id, part_type=None):
    """
    Returns the full part data dict for a given part ID.
    
    Args:
        part_id: Internal part ID (e.g., "xs_0_engine")
        part_type: Optional type hint ("engines", "breaks", "boosts")
    
    Returns:
        Part data dict or None if not found
    """
    data = _load_game_data()
    parts = data.get("parts", {})
    
    # If type hint provided, search that category first
    if part_type:
        return parts.get(part_type, {}).get(part_id)
    
    # Search all categories
    for category in ["engines", "breaks", "boosts"]:
        if part_id in parts.get(category, {}):
            return parts[category][part_id]
    
    return None


def get_part_display_name(part_id, lang_manager, part_type=None):
    """
    Returns the localized display name for a part.
    Falls back to part_id if not found.
    
    Args:
        part_id: Internal part ID (e.g., "xs_0_engine")
        lang_manager: LanguageManager instance for localization
        part_type: Optional type hint ("engines", "breaks", "boosts")
    
    Returns:
        Localized display name string
    """
    part_data = get_part_data(part_id, part_type)
    if part_data and "name_key" in part_data:
        return lang_manager.get(part_data["name_key"])
    return part_id  # Fallback to internal ID


def get_part_size_class(part_id, part_type=None):
    """
    Returns the size class for a part from game data.
    Falls back to extraction from ID if not in data.
    """
    part_data = get_part_data(part_id, part_type)
    if part_data and "size_class" in part_data:
        return part_data["size_class"]
    
    # Fallback: extract from ID prefix
    if not part_id or not isinstance(part_id, str):
        return None
    
    parts = part_id.lower().split('_')
    if len(parts) >= 1:
        size = parts[0].upper()
        if size in ('XS', 'S', 'M', 'L', 'XL'):
            return size
    
    return None


def is_part_compatible(car_id, part_id):
    """
    Checks if a part is compatible with a car based on their size classes.
    """
    car_class = get_car_size_class(car_id)
    part_class = get_part_size_class(part_id)

    if part_class is None:
        return False

    return car_class == part_class


def get_car_price(car_id):
    """
    Returns the price for a car.
    Returns 0 if car not found.
    """
    car_data = get_car_data(car_id)
    if car_data:
        return car_data.get("price", 0)
    return 0


def get_part_price(part_id, part_type=None):
    """
    Returns the price for a part.
    Returns 0 if part not found.
    """
    part_data = get_part_data(part_id, part_type)
    if part_data:
        return part_data.get("price", 0)
    return 0


def get_all_cars():
    """
    Returns a list of all car IDs available in the game.
    """
    data = _load_game_data()
    return list(data.get("cars", {}).keys())


def get_all_parts(part_type=None):
    """
    Returns a list of all part IDs, optionally filtered by type.

    Args:
        part_type: Optional filter ("engines", "breaks", "boosts")

    Returns:
        List of part IDs
    """
    data = _load_game_data()
    parts = data.get("parts", {})

    if part_type:
        return list(parts.get(part_type, {}).keys())

    # Return all parts from all categories
    all_parts = []
    for category in ["engines", "breaks", "boosts"]:
        all_parts.extend(parts.get(category, {}).keys())
    return all_parts


def get_parts_by_size_class(size_class, part_type=None):
    """
    Returns parts filtered by size class.

    Args:
        size_class: Size class to filter by ("XS", "S", "M")
        part_type: Optional type filter ("engines", "breaks", "boosts")

    Returns:
        List of part IDs matching the criteria
    """
    all_parts = get_all_parts(part_type)
    return [p for p in all_parts if get_part_size_class(p) == size_class]


# --- TRACK/MAP DATA ---

def get_all_tracks():
    """Returns a list of all track IDs available in the game."""
    data = _load_game_data()
    return list(data.get("tracks", {}).keys())


def get_track_data(track_id):
    """
    Returns the full track data dict for a given track ID.
    
    Automatically normalizes track_id to handle various formats:
    - Integer: 0 → "track_0"
    - String digit: "0" → "track_0"
    - Proper ID: "track_0" → "track_0"
    
    Args:
        track_id: Track identifier (int, str digit, or str ID)
    
    Returns:
        dict: Track data or None if not found
    """
    # DODANO: Normalizacja track_id
    original_id = track_id
    track_id = normalize_track_id(track_id)
    
    data = _load_game_data()
    
    # Debug logging
    if original_id != track_id:
        logging.info(f"[GAME_DATA] Normalized track_id: {original_id} → {track_id}")
    
    result = data.get("tracks", {}).get(track_id)
    
    if result is None:
        logging.error(f"[GAME_DATA] Track not found: {track_id}")
        # ✅ FIX: Do not fallback to track_0 silently. Return None to let caller handle it.
        # This prevents loading Map 1 config when Map 0 is requested but has a typo.
        return None
    
    return result


def get_track_display_name(track_id, lang_manager):
    """Returns the localized display name for a track."""
    track_data = get_track_data(track_id)
    if track_data and "name_key" in track_data:
        return lang_manager.get(track_data["name_key"])
    return track_id


# --- OPPONENT DATA ---

def get_all_opponents():
    """Returns a list of all opponent IDs."""
    data = _load_game_data()
    return list(data.get("opponents", {}).keys())


def get_opponent_data(opponent_id):
    """Returns the opponent data dict."""
    data = _load_game_data()
    return data.get("opponents", {}).get(opponent_id)


def get_opponent_display_name(opponent_id, lang_manager):
    """Returns the localized display name for an opponent."""
    opp_data = get_opponent_data(opponent_id)
    if opp_data and "name_key" in opp_data:
        return lang_manager.get(opp_data["name_key"])
    return opponent_id


# --- CAR STATS CALCULATION ---

def get_car_stats(car_id, mounted_parts=None):
    """
    Calculates the effective stats for a car with mounted parts using
    power-to-weight ratio for realistic physics.

    Args:
        car_id: The car's model_id
        mounted_parts: Dict of mounted parts {"engine": "xs_0_engine", "breaks": ...}

    Returns:
        Dict with calculated stats including horsepower, weight, top_speed_kmh,
        braking_efficiency, grip_factor, boost_power
    """
    # Get car data
    car_data = get_car_data(car_id)
    if not car_data:
        # Fallback defaults
        car_weight = 1200
    else:
        car_weight = car_data.get('weight_kg', 1200)
    
    # Initialize stats
    stats = {
        'name': car_id,
        'weight': car_weight,  # Base car weight
        'horsepower': 0,
        'top_speed_kmh': 180,  # Base top speed without engine
        'braking_efficiency': 0.5,  # Base braking (50%)
        'grip_factor': 1.0,  # Base grip
        'boost_power': 0,
        'cooling_efficiency': 1.0,
        'has_engine': False,
        'has_brakes': False
    }

    # Total mass calculation (car + engine)
    total_mass = car_weight
    
    # Check for components presence
    # If mounted_parts is explicitly provided (even empty), check for keys.
    # If None, assume default state (has parts) to support legacy/debug calls.
    if mounted_parts is not None:
        has_engine = 'engine' in mounted_parts and mounted_parts['engine'] is not None
        has_brakes = 'breaks' in mounted_parts and mounted_parts['breaks'] is not None
        has_boost = 'boost' in mounted_parts and mounted_parts['boost'] is not None
    else:
        # Legacy behavior: assume parts are present
        has_engine = True
        has_brakes = True
        has_boost = True

    stats['has_engine'] = has_engine
    stats['has_brakes'] = has_brakes

    if not has_engine:
        # No engine = car is unusable
        stats['horsepower'] = 0
        stats['top_speed_kmh'] = 0
        return stats

    # Process mounted parts
    if mounted_parts:
        # Engine
        engine_id = mounted_parts.get('engine')
        if engine_id:
            engine_data = get_part_data(engine_id, 'engines')
            if engine_data:
                stats['horsepower'] = engine_data.get('horsepower', 100)
                engine_weight = engine_data.get('weight_kg', 100)
                total_mass += engine_weight

        # Brakes
        brakes_id = mounted_parts.get('breaks')
        if brakes_id:
            brakes_data = get_part_data(brakes_id, 'breaks')
            if brakes_data:
                stats['braking_efficiency'] = brakes_data.get('braking_power', 0.7)

        # Boost
        boost_id = mounted_parts.get('boost')
        if boost_id:
            boost_data = get_part_data(boost_id, 'boosts')
            if boost_data:
                stats['boost_power'] = boost_data.get('boost_power', 50)

    # Calculate derived stats using power-to-weight ratio
    stats['weight'] = total_mass
    
    if stats['horsepower'] > 0:
        # Power-to-weight ratio (HP per kg)
        power_to_weight = stats['horsepower'] / total_mass
        
        # Top speed calculation (realistic formula)
        # Base: 180 km/h, scales with power-to-weight
        # Formula: speed = base * sqrt(power_to_weight / reference_ratio)
        reference_ratio = 0.1  # Reference: 100 HP / 1000 kg
        stats['top_speed_kmh'] = 180 * (power_to_weight / reference_ratio) ** 0.5
        
        # Clamp to reasonable range
        stats['top_speed_kmh'] = max(120, min(stats['top_speed_kmh'], 350))

    return stats