"""
TMX Map Loader Module
=====================
Parses Tiled TMX map files for the racing game.
Extracts tile layers, collision objects, and tileset data.
Refactored for server compatibility (no mandatory Pygame dependency).
"""
import os
import xml.etree.ElementTree as ET
from typing import List, Dict, Any

# --- Pygame Dependency Check and Mocking (for server-side metadata loading) ---
try:
    import pygame as pg
    IS_PYGAME_AVAILABLE = True
except ImportError:
    IS_PYGAME_AVAILABLE = False
    
    # Mock necessary Pygame classes for graceful server execution
    class MockSurface:
        def __init__(self, *args, **kwargs): pass
        def get_width(self): return 0
        def get_height(self): return 0
        def subsurface(self, rect): return self
        def copy(self): return self
    class MockRect:
        def __init__(self, *args, **kwargs): pass
        def collidepoint(self, x, y): return False
    
    # Create a mock Pygame module
    pg = type('MockPG', (object,), {
        'image': type('MockImage', (object,), {'load': lambda x: MockSurface(), 'convert_alpha': lambda s: s}),
        'Surface': lambda size, flags: MockSurface(),
        'Rect': MockRect,
        'SRCALPHA': 0,
        'transform': type('MockTransform', (object,), {'scale': lambda s, size: s}),
    })


class Tileset:
    """Represents a tileset with image and tile data."""
    
    def __init__(self, firstgid, name, tile_width, tile_height, image_path, columns):
        self.firstgid = firstgid
        self.name = name
        self.tile_width = tile_width
        self.tile_height = tile_height
        self.image_path = image_path
        self.columns = columns
        self.image = None
        self.tiles = {}  # gid -> surface
    
    def load_image(self, base_path):
        """Load the tileset image and split into individual tiles."""
        if not IS_PYGAME_AVAILABLE:
            print(f"[TMX] Server mode: skipping image load for {self.name}")
            return False

        full_path = os.path.join(base_path, self.image_path)
        if not os.path.exists(full_path):
            print(f"[TMX] Warning: Tileset image not found: {full_path}")
            return False
        
        self.image = pg.image.load(full_path).convert_alpha()
        
        # Calculate number of rows
        img_height = self.image.get_height()
        rows = img_height // self.tile_height
        
        # Extract individual tiles
        gid = self.firstgid
        for row in range(rows):
            for col in range(self.columns):
                x = col * self.tile_width
                y = row * self.tile_height
                rect = pg.Rect(x, y, self.tile_width, self.tile_height)
                tile_surface = self.image.subsurface(rect).copy()
                self.tiles[gid] = tile_surface
                gid += 1
        
        print(f"[TMX] Loaded tileset '{self.name}' with {len(self.tiles)} tiles")
        return True
    
    def get_tile(self, gid):
        """Get a tile surface by global ID."""
        return self.tiles.get(gid)


class TileLayer:
    """Represents a tile layer with CSV data."""
    
    def __init__(self, name, width, height, data):
        self.name = name
        self.width = width
        self.height = height
        self.data = data  # 2D list of tile GIDs
    
    def get_tile_at(self, x, y):
        """Get tile GID at grid position."""
        if 0 <= x < self.width and 0 <= y < self.height:
            return self.data[y][x]
        return 0


class CollisionObject:
    """Represents a collision object (polygon, polyline, or rectangle)."""

    def __init__(self, obj_id, name, x, y, obj_type='rect', width=0, height=0, points=None, group_name=None):
        self.id = obj_id
        self.name = name
        self.group_name = group_name
        self.x = x
        self.y = y
        self.type = obj_type  # 'rect', 'polygon', 'polyline', 'point'
        self.width = width
        self.height = height
        self.points = points or []  # List of (x, y) tuples relative to object position

    def get_world_points(self):
        """Get polygon/polyline points in world coordinates."""
        if self.type in ('polygon', 'polyline'):
            return [(self.x + px, self.y + py) for px, py in self.points]
        elif self.type == 'rect':
            # Rectangle as 4 corner points
            return [
                (self.x, self.y),
                (self.x + self.width, self.y),
                (self.x + self.width, self.y + self.height),
                (self.x, self.y + self.height)
            ]
        return []


class TMXMap:
    """
    Represents a complete TMX map with layers, tilesets, and collision data.
    """

    # Special tile IDs (GID values in the map)
    TILE_START_LINE = 6
    TILE_FINISH_LINE = 9
    ROAD_TILES = {3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16}
    SHOULDER_TILES = {1, 2, 17, 18, 19, 20, 21, 22, 23, 24} 

    def __init__(self, scale=1.0):
        self.scale = scale

        self.width = 0
        self.height = 0
        self.tile_width = 64
        self.tile_height = 64
        self.base_tile_width = 64
        self.base_tile_height = 64
        self.pixel_width = 0
        self.pixel_height = 0
        
        # New map metadata fields (Task 1.3)
        self.map_name = ""
        self.laps = 3 # Default: 3 laps
        self.is_p2p = False # Default: False
        self.properties: Dict[str, Any] = {} # Store all map properties

        self.tilesets = []
        self.tile_layers = []
        self.collision_objects = []
        self.waypoints = [] # Checkpoints for server validation
        self.racing_line = [] 

        self.rendered_surface = None 

        self.start_line_pos = None 
        self.finish_line_pos = None 
        self.start_line_rect = None  
        self.finish_line_rect = None  

        self.road_layer = None

    @property
    def world_width(self):
        return self.pixel_width

    @property
    def world_height(self):
        return self.pixel_height

    def get_tileset_for_gid(self, gid):
        """Find the tileset that contains a given global tile ID."""
        if gid == 0:
            return None
        for tileset in reversed(self.tilesets):
            if gid >= tileset.firstgid:
                return tileset
        return None

    def find_special_tiles(self):
        """Find and store positions of start/finish line tiles."""
        if not self.tile_layers:
            return
            
        for layer in self.tile_layers:
            if layer.name.lower() in ('droga', 'road', 'track'):
                self.road_layer = layer

            for y in range(layer.height):
                for x in range(layer.width):
                    gid = layer.get_tile_at(x, y)

                    # Calculate pixel center of tile
                    px = x * self.tile_width + self.tile_width // 2
                    py = y * self.tile_height + self.tile_height // 2

                    if gid == self.TILE_START_LINE:
                        self.start_line_pos = (px, py)
                        if IS_PYGAME_AVAILABLE:
                            self.start_line_rect = pg.Rect(
                                x * self.tile_width, y * self.tile_height,
                                self.tile_width, self.tile_height
                            )
                        print(f"[TMX] Found start line at tile ({x}, {y}) -> pixel ({px}, {py})")

                    elif gid == self.TILE_FINISH_LINE:
                        self.finish_line_pos = (px, py)
                        if IS_PYGAME_AVAILABLE:
                            self.finish_line_rect = pg.Rect(
                                x * self.tile_width, y * self.tile_height,
                                self.tile_width, self.tile_height
                            )
                        print(f"[TMX] Found finish line at tile ({x}, {y}) -> pixel ({px}, {py})")

    def get_tile_at_pixel(self, px, py):
        """Get the tile GID at a pixel position from the road layer."""
        if self.road_layer is None:
            return 0

        # Convert pixel to tile coordinates
        tx = int(px) // self.tile_width
        ty = int(py) // self.tile_height

        return self.road_layer.get_tile_at(tx, ty)

    def is_on_road(self, px, py):
        """Check if a pixel position is on a road tile."""
        gid = self.get_tile_at_pixel(px, py)
        return gid in self.ROAD_TILES

    def is_on_start_line(self, px, py):
        """Check if a position is on the start line tile."""
        if IS_PYGAME_AVAILABLE and self.start_line_rect:
            return self.start_line_rect.collidepoint(px, py)
        return False

    def is_on_finish_line(self, px, py):
        """Check if a position is on the finish line tile."""
        if IS_PYGAME_AVAILABLE and self.finish_line_rect:
            return self.finish_line_rect.collidepoint(px, py)
        return False

    def get_start_position(self):
        """Get the start line center position, or fallback."""
        if self.start_line_pos:
            return self.start_line_pos
        return (self.pixel_width // 2, self.pixel_height // 2)

    def get_start_angle(self):
        """
        Determine the track direction at the start line by analyzing surrounding tiles.
        Returns angle in degrees (0=right, -90=up, 90=down, 180=left).
        """
        if not self.start_line_pos or not self.road_layer:
            return -90  # Default: facing up

        # Get tile coordinates of start line
        tx = int(self.start_line_pos[0]) // self.tile_width
        ty = int(self.start_line_pos[1]) // self.tile_height

        # Check adjacent tiles to determine road direction
        above = self.road_layer.get_tile_at(tx, ty - 1)
        below = self.road_layer.get_tile_at(tx, ty + 1)
        left = self.road_layer.get_tile_at(tx - 1, ty)
        right = self.road_layer.get_tile_at(tx + 1, ty)

        above_road = above in self.ROAD_TILES
        below_road = below in self.ROAD_TILES
        left_road = left in self.ROAD_TILES
        right_road = right in self.ROAD_TILES

        # Determine direction based on road connectivity
        if above_road and not below_road:
            return -90
        elif below_road and not above_road:
            return 90
        elif right_road and not left_road:
            return 0
        elif left_road and not right_road:
            return 180
        elif above_road:
            return -90

        return -90

    def get_tile_surface(self, gid):
        """Get the surface for a tile by its GID."""
        if not IS_PYGAME_AVAILABLE:
            return None
        tileset = self.get_tileset_for_gid(gid)
        if tileset:
            return tileset.get_tile(gid)
        return None

    def render_to_surface(self):
        """Pre-render all tile layers to a single surface with scaling applied."""
        if not IS_PYGAME_AVAILABLE:
            print("[TMX] Server mode: skipping map rendering.")
            self.rendered_surface = None
            return None
            
        self.rendered_surface = pg.Surface(
            (self.pixel_width, self.pixel_height),
            pg.SRCALPHA
        )
        self.rendered_surface.fill((40, 40, 45)) 

        for layer in self.tile_layers:
            for y in range(layer.height):
                for x in range(layer.width):
                    gid = layer.get_tile_at(x, y)
                    if gid > 0:
                        tile_surf = self.get_tile_surface(gid)
                        if tile_surf:
                            if self.scale != 1.0:
                                scaled_size = (self.tile_width, self.tile_height)
                                tile_surf = pg.transform.scale(tile_surf, scaled_size)

                            px = x * self.tile_width
                            py = y * self.tile_height
                            self.rendered_surface.blit(tile_surf, (px, py))

        print(f"[TMX] Rendered map surface: {self.pixel_width}x{self.pixel_height} (scale: {self.scale}x)")
        return self.rendered_surface

    def point_in_collision(self, x, y):
        """Check if a point collides with any collision object (polygon area)."""
        for obj in self.collision_objects:
            if obj.type == 'point':
                continue
            if obj.type == 'polyline':
                continue 

            points = obj.get_world_points()
            if len(points) < 3:
                continue

            if self._point_in_polygon(x, y, points):
                return True
        return False

    def _point_in_polygon(self, x, y, polygon):
        """Ray casting algorithm for point-in-polygon test."""
        n = len(polygon)
        inside = False

        j = n - 1
        for i in range(n):
            xi, yi = polygon[i]
            xj, yj = polygon[j]

            if ((yi > y) != (yj > y)) and (x < (xj - xi) * (y - yi) / (yj - yi) + xi):
                inside = not inside
            j = i

        return inside
    
    def _point_to_segment_distance(self, px, py, p1, p2):
        """
        Calculate distance from point to line segment and return normal vector.
        Returns (distance, normal_vector).
        """
        import math

        x1, y1 = p1
        x2, y2 = p2

        # Vector from p1 to p2
        dx = x2 - x1
        dy = y2 - y1

        # Length squared
        len_sq = dx * dx + dy * dy

        if len_sq == 0:
            # p1 and p2 are the same point
            dist = math.sqrt((px - x1) ** 2 + (py - y1) ** 2)
            if dist > 0:
                return dist, ((px - x1) / dist, (py - y1) / dist)
            return 0, (0, 0)

        # Parameter t for projection onto line
        t = max(0, min(1, ((px - x1) * dx + (py - y1) * dy) / len_sq))

        # Closest point on segment
        closest_x = x1 + t * dx
        closest_y = y1 + t * dy

        # Distance to closest point
        dist_x = px - closest_x
        dist_y = py - closest_y
        dist = math.sqrt(dist_x * dist_x + dist_y * dist_y)

        # Normal pointing away from the line
        if dist > 0:
            normal = (dist_x / dist, dist_y / dist)
        else:
            # Point is exactly on the line, use perpendicular
            length = math.sqrt(len_sq)
            normal = (-dy / length, dx / length)

        return dist, normal

    def point_near_edge(self, x, y, threshold=5.0):
        """
        Check if a point is near any collision edge (for polyline collision).
        Returns (is_colliding, push_vector) where push_vector is the direction to push away.
        """
        min_dist = float('inf')
        nearest_normal = (0, 0)

        for obj in self.collision_objects:
            points = obj.get_world_points()
            if len(points) < 2:
                continue

            # For polylines, don't close the loop
            num_edges = len(points) - 1 if obj.type == 'polyline' else len(points)

            for i in range(num_edges):
                p1 = points[i]
                p2 = points[(i + 1) % len(points)]

                dist, normal = self._point_to_segment_distance(x, y, p1, p2)
                if dist < min_dist:
                    min_dist = dist
                    nearest_normal = normal

        if min_dist < threshold:
            return True, nearest_normal
        return False, (0, 0)

    def get_collision_edges(self):
        """Get all collision polygon/polyline edges for line-segment collision checks."""
        edges = []
        for obj in self.collision_objects:
            points = obj.get_world_points()
            if len(points) < 2:
                continue

            # For polylines, don't close the loop
            num_edges = len(points) - 1 if obj.type == 'polyline' else len(points)

            for i in range(num_edges):
                p1 = points[i]
                p2 = points[(i + 1) % len(points)]
                edges.append((p1, p2))

        return edges


def load_tmx(filepath, scale=10.0, is_server=False):
    """
    Load a TMX file and return a TMXMap object.

    Args:
        filepath: Path to the .tmx file
        scale: Scale factor for the map (default 10.0 for proper car proportions)
        is_server: If True, skips Pygame-dependent operations.

    Returns:
        TMXMap object or None if loading failed
    """
    if not os.path.exists(filepath):
        print(f"[TMX] Error: File not found: {filepath}")
        return None

    base_path = os.path.dirname(filepath)

    try:
        tree = ET.parse(filepath)
        root = tree.getroot()
    except ET.ParseError as e:
        print(f"[TMX] Error parsing XML: {e}")
        return None

    tmx_map = TMXMap(scale=scale)

    # Parse map attributes
    tmx_map.width = int(root.get('width', 0))
    tmx_map.height = int(root.get('height', 0))
    tmx_map.base_tile_width = int(root.get('tilewidth', 64))
    tmx_map.base_tile_height = int(root.get('tileheight', 64))

    # Apply scale to tile dimensions and pixel dimensions
    tmx_map.tile_width = int(tmx_map.base_tile_width * scale)
    tmx_map.tile_height = int(tmx_map.base_tile_height * scale)
    tmx_map.pixel_width = tmx_map.width * tmx_map.tile_width
    tmx_map.pixel_height = tmx_map.height * tmx_map.tile_height
    
    # Parse map properties (Task 1.3)
    properties_elem = root.find('properties')
    if properties_elem is not None:
        for prop_elem in properties_elem.findall('property'):
            name = prop_elem.get('name')
            value = prop_elem.get('value')
            if value is not None:
                tmx_map.properties[name] = value
                
    # Extract specific properties for the Room class
    tmx_map.map_name = os.path.basename(filepath).replace(".tmx", "")
    tmx_map.laps = int(tmx_map.properties.get('laps', 3))
    # Convert string property "true"/"false" to boolean
    tmx_map.is_p2p = tmx_map.properties.get('is_p2p', 'False').lower() == 'true'


    print(f"[TMX] Loading map: {tmx_map.width}x{tmx_map.height} tiles, "
          f"base {tmx_map.base_tile_width}x{tmx_map.base_tile_height}px -> "
          f"scaled {tmx_map.tile_width}x{tmx_map.tile_height}px, "
          f"total {tmx_map.pixel_width}x{tmx_map.pixel_height} pixels")

    # Parse tilesets
    for tileset_elem in root.findall('tileset'):
        _parse_tileset(tileset_elem, base_path, tmx_map, is_server)

    # Parse layers
    for layer_elem in root.findall('layer'):
        _parse_tile_layer(layer_elem, tmx_map)

    # Parse object groups (collision layers) - scale collision objects
    for objgroup_elem in root.findall('objectgroup'):
        _parse_object_group(objgroup_elem, tmx_map, scale)

    # Find special tiles (start/finish lines) and road layer
    tmx_map.find_special_tiles()
    
    # Pre-render the map (only on client side)
    if not is_server:
        tmx_map.render_to_surface()

    return tmx_map


def _parse_tileset(elem, base_path, tmx_map, is_server):
    """Parse a tileset element (either embedded or external)."""
    firstgid = int(elem.get('firstgid', 1))
    source = elem.get('source')

    if source:
        # External tileset (.tsx file)
        tsx_path = os.path.join(base_path, source)
        if not os.path.exists(tsx_path):
            print(f"[TMX] Warning: External tileset not found: {tsx_path}")
            return

        tsx_base = os.path.dirname(tsx_path)
        try:
            tsx_tree = ET.parse(tsx_path)
            tsx_root = tsx_tree.getroot()
        except ET.ParseError as e:
            print(f"[TMX] Error parsing tileset: {e}")
            return

        name = tsx_root.get('name', 'unnamed')
        tile_width = int(tsx_root.get('tilewidth', 64))
        tile_height = int(tsx_root.get('tileheight', 64))
        columns = int(tsx_root.get('columns', 8))

        # Get image source
        image_elem = tsx_root.find('image')
        if image_elem is not None:
            image_source = image_elem.get('source', '')
        else:
            print(f"[TMX] Warning: No image in tileset {name}")
            return
    else:
        # Embedded tileset
        name = elem.get('name', 'unnamed')
        tile_width = int(elem.get('tilewidth', 64))
        tile_height = int(elem.get('tileheight', 64))
        columns = int(elem.get('columns', 8))
        tsx_base = base_path

        image_elem = elem.find('image')
        if image_elem is not None:
            image_source = image_elem.get('source', '')
        else:
            return

    tileset = Tileset(firstgid, name, tile_width, tile_height, image_source, columns)
    if not is_server:
        tileset.load_image(tsx_base)
    tmx_map.tilesets.append(tileset)


def _parse_tile_layer(elem, tmx_map):
    """Parse a tile layer element."""
    name = elem.get('name', 'unnamed')
    width = int(elem.get('width', tmx_map.width))
    height = int(elem.get('height', tmx_map.height))

    data_elem = elem.find('data')
    if data_elem is None:
        return

    encoding = data_elem.get('encoding', 'csv')
    if encoding != 'csv':
        print(f"[TMX] Warning: Unsupported encoding '{encoding}', only CSV supported")
        return

    # Parse CSV data
    csv_text = data_elem.text.strip()
    rows = []
    for line in csv_text.split('\n'):
        line = line.strip().rstrip(',')
        if line:
            row = [int(x) for x in line.split(',') if x.strip()]
            rows.append(row)

    layer = TileLayer(name, width, height, rows)
    tmx_map.tile_layers.append(layer)
    print(f"[TMX] Loaded tile layer '{name}': {width}x{height}")


def _parse_object_group(elem, tmx_map, scale=1.0):
    """Parse an object group (collision layer) with scaling applied."""
    import math
    group_name = elem.get('name', 'unnamed')

    for obj_elem in elem.findall('object'):
        obj_id = int(obj_elem.get('id', 0))
        name = obj_elem.get('name', '')

        # Apply scale to object positions and dimensions
        x = float(obj_elem.get('x', 0)) * scale
        y = float(obj_elem.get('y', 0)) * scale
        width = float(obj_elem.get('width', 0)) * scale
        height = float(obj_elem.get('height', 0)) * scale

        # Get rotation in degrees (Tiled uses clockwise rotation)
        rotation = float(obj_elem.get('rotation', 0))

        # Check for polygon
        polygon_elem = obj_elem.find('polygon')
        obj = None
        if polygon_elem is not None:
            points_str = polygon_elem.get('points', '')
            points = _parse_points(points_str, scale, rotation)
            obj = CollisionObject(obj_id, name, x, y, 'polygon', points=points, group_name=group_name)

        # Check for polyline (open path, not closed polygon)
        if obj is None:
            polyline_elem = obj_elem.find('polyline')
            if polyline_elem is not None:
                points_str = polyline_elem.get('points', '')
                points = _parse_points(points_str, scale, rotation)
                obj = CollisionObject(obj_id, name, x, y, 'polyline', points=points, group_name=group_name)

        # Rectangle or point
        if obj is None:
            if width > 0 and height > 0:
                obj = CollisionObject(obj_id, name, x, y, 'rect', width, height, group_name=group_name)
            else:
                obj = CollisionObject(obj_id, name, x, y, 'point', group_name=group_name)

        if obj:
            # Make waypoint layer detection more flexible
            if group_name.lower() in ('waypoints', 'path', 'checkpoints'):
                tmx_map.waypoints.append(obj)
            elif group_name.lower() in ('racingline', 'racing_line', 'line'):
                # Extract points from polyline object for racing line
                if obj.type == 'polyline':
                    world_points = obj.get_world_points()
                    tmx_map.racing_line = world_points
                    print(f"[TMX] Loaded RacingLine with {len(world_points)} points")
                else:
                    # Treat points in racing line group as waypoints
                    tmx_map.waypoints.append(obj)
            else:
                tmx_map.collision_objects.append(obj)

    # Separate logging for clarity
    if group_name.lower() in ('waypoints', 'path', 'checkpoints'):
        print(f"[TMX] Loaded waypoint group '{group_name}': {len(tmx_map.waypoints)} waypoints")
    elif group_name.lower() not in ('racingline', 'racing_line', 'line'):
        print(f"[TMX] Loaded object group '{group_name}': {len(tmx_map.collision_objects)} objects")


def _parse_points(points_str, scale=1.0, rotation=0.0):
    """Parse a points string like '0,0 10,20 30,40' into list of tuples with scaling and rotation."""
    import math
    points = []

    angle_rad = math.radians(rotation)
    cos_a = math.cos(angle_rad)
    sin_a = math.sin(angle_rad)

    for pair in points_str.split():
        parts = pair.split(',')
        if len(parts) == 2:
            px = float(parts[0]) * scale
            py = float(parts[1]) * scale

            if rotation != 0:
                # Rotation logic is complex and often incorrect when applied to TMX local points.
                # Assuming rotation is 0 for TMX objects unless proven otherwise by Tiled map usage.
                # For safety, we keep the rotation logic from the original file if it was working.
                rx = px * cos_a - py * sin_a
                ry = px * sin_a + py * cos_a
                points.append((rx, ry))
            else:
                points.append((px, py))
    return points


def load_map_metadata_server(map_name: str, base_dir: str = 'maps/', file_ext: str = '.tmx') -> Dict:
    """
    (Task 1.3 Helper) Loads essential map metadata (checkpoints, laps, p2p status) for server-side validation.
    
    Args:
        map_name: The base name of the map file (e.g., 'map_0').
        base_dir: The directory where map files are stored (e.g., 'maps/').
        file_ext: The expected file extension.
        
    Returns:
        A dictionary with map configuration.
    """
    filepath = os.path.join(base_dir, f"{map_name}{file_ext}")
    
    if file_ext == '.tmx':
        # Use a scale of 10.0, as it was the default in the original load_tmx, suggesting
        # that the game's physics operates on scaled coordinates.
        SERVER_SCALE = 10.0 
        tmx_map = load_tmx(filepath, scale=SERVER_SCALE, is_server=True) 
        if not tmx_map:
            # Fallback if map loading fails
            return {"checkpoints": [], "laps": 3, "map_name": map_name, "is_p2p": False}
        
        # Simplify waypoints: extract only coordinates and ID
        checkpoints = [
            {
                "id": obj.id,
                "x": obj.x, 
                "y": obj.y,
            } for obj in tmx_map.waypoints if obj.type in ('point', 'rect')
        ]
        
        # Fallback: if waypoints are polylines (racing line) - convert to discrete points
        if not checkpoints and tmx_map.racing_line:
            checkpoints = [
                {
                    "id": i + 1, 
                    "x": x, 
                    "y": y, 
                } for i, (x, y) in enumerate(tmx_map.racing_line)
            ]
        
        return {
            "checkpoints": checkpoints,
            "laps": tmx_map.laps,
            "map_name": tmx_map.map_name,
            "is_p2p": tmx_map.is_p2p
        }
    
    # Placeholder for .json maps if they were requested (data/nav/)
    if file_ext == '.json':
        # Logic for loading JSON map data (not implemented as no data/nav/ files were shown)
        pass
        
    return {"checkpoints": [], "laps": 3, "map_name": map_name, "is_p2p": False}