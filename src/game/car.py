"""
Car Physics Module
==================
Implements car physics including acceleration, braking, steering, and momentum.
Uses realistic EnginePhysics module for force calculations.
"""
import pygame as pg
import math
from src.constants import (
    ACCENT_GREEN, ACCENT_RED,
    TEMP_AMBIENT, TEMP_OPTIMAL, TEMP_WARNING, TEMP_OVERHEAT, TEMP_FAILURE,
    WEAR_TIRE_NORMAL, WEAR_TIRE_DRIFT, WEAR_BRAKE_NORMAL, WEAR_SUSPENSION_IMPACT,
    PENALTY_GRIP_TIRES, PENALTY_BRAKE_FADE, PENALTY_POWER_OVERHEAT
)
from src.game.car_audio import CarAudioController
from src.game.particles import TireSmokeEmitter, SparkEmitter
from src.physics.engine_physics import EnginePhysics
from src.physics.pixel_collision import get_collision_system


class Car:
    """
    Represents a car with physics-based movement.
    Uses a simple 2D car physics model with:
    - Velocity-based movement
    - Angular velocity for steering
    - Friction and drag
    - Collision response
    """

    def __init__(self, x, y, angle, stats, sprite=None, is_player=True):
        # Position and rotation
        self.x = float(x)
        self.y = float(y)
        self.angle = float(angle)  # Degrees, 0 = right, 90 = down
        self.start_pos = pg.Vector2(x, y)
        self.previous_position = pg.Vector2(x, y)
        self.last_asphalt_pos = pg.Vector2(x, y)
        self.last_asphalt_angle = angle
        self.last_checkpoint_pos = pg.Vector2(x, y)

        # Velocity
        self.velocity = pg.Vector2(0.0, 0.0)
        self.angular_velocity = 0.0
        
        # Control inputs (for player and AI)
        self.throttle = 0.0  # 0.0 to 1.0 (gas pedal position)
        self.steering = 0.0  # -1.0 to 1.0
        self.is_braking = 0.0

        # Stats from car + parts
        # PHYSICS REBALANCE: Using realistic EnginePhysics module
        # 1. STORE RAW DATA FROM JSON WITH VALIDATION
        self.hp = float(stats.get('horsepower', 150))
        self.mass = float(stats.get('weight', 1200))
        engine_weight = float(stats.get('engine_weight_kg', 100) if 'engine_weight_kg' in stats else 100)
        self.braking_efficiency = float(stats.get('braking_efficiency', 1.0))
        self.grip_factor = float(stats.get('grip_factor', 1.0))
        
        # CRITICAL VALIDATION - Ensure we have valid physics data
        if self.hp <= 0:
            print(f"[CAR WARNING] Invalid horsepower: {self.hp}, using fallback 150 HP")
            self.hp = 150.0
        if self.mass <= 0:
            print(f"[CAR WARNING] Invalid mass: {self.mass}, using fallback 1200 kg")
            self.mass = 1200.0
        if engine_weight <= 0:
            print(f"[CAR WARNING] Invalid engine weight: {engine_weight}, using fallback 100 kg")
            engine_weight = 100.0
        
        # DEBUG: Print what we actually received
        print(f"[CAR DATA] Received stats:")
        print(f"  - HP: {self.hp}, Mass: {self.mass}, Engine: {engine_weight}kg")
        print(f"  - Braking: {self.braking_efficiency}, Grip: {self.grip_factor}")
        print(f"  - Has engine: {stats.get('has_engine', 'N/A')}, Has brakes: {stats.get('has_brakes', 'N/A')}")
        
        # Preserve other stats
        self.boost_power = stats.get('boost_power', 0)
        self.cooling_efficiency = stats.get('cooling_efficiency', 1.0)
        self.grip_efficiency = self.grip_factor # Use the new factor for existing logic

        # 2. INITIALIZE REALISTIC PHYSICS ENGINE
        # This module handles all force calculations with proper drag, friction, etc.
        self.physics_engine = EnginePhysics(
            power_hp=self.hp,
            weight_kg=self.mass,
            engine_weight_kg=engine_weight
        )
        
        # Get calculated values from physics engine
        self.max_kmh = self.physics_engine.calculate_top_speed_kmh()
        self.max_speed_units = self.physics_engine.to_game_units(
            self.physics_engine.calculate_top_speed_ms(),
            is_speed=True
        )
        self.power_to_weight = self.physics_engine.power_to_weight
        
        # Debug Print to verify calculation
        physics_stats = self.physics_engine.get_stats_summary()
        print(f"CAR INIT: {stats.get('name', 'Unknown')}")
        print(f" -> Mass: {physics_stats['total_mass_kg']:.0f}kg | HP: {physics_stats['power_hp']:.0f} | P/W: {physics_stats['power_to_weight']:.1f} HP/ton")
        print(f" -> TopSpeed: {physics_stats['top_speed_kmh']:.1f} km/h | 0-100: {physics_stats['accel_0_100_time']:.2f}s")

        # Component presence checks
        self.has_engine = stats.get('has_engine', True)
        self.has_brakes = stats.get('has_brakes', True)
        
        # ACCELERATION TUNING
        # Reduce acceleration to make it more realistic and controllable
        # Default: 0.3 (30% of calculated acceleration)
        # Adjust this value to taste:
        #   - 0.2 = Very slow, heavy feel (realistic city car)
        #   - 0.3 = Moderate acceleration (recommended for gameplay)
        #   - 0.5 = Quick acceleration (sporty feel)
        #   - 1.0 = Full physics engine calculation (very fast)
        self.accel_multiplier = 0.3

        # Physics constants
        # Adjusted for Arcade feel (High grip, fast deceleration)
        self.base_friction = 0.99  # Higher friction to prevent "ice" feel
        self.friction = self.base_friction
        self.drag = 0.002  # Higher drag
        self.turn_speed = 4.5 * self.grip_efficiency  # Sharper turning
        self.min_turn_speed_factor = 0.3  # Minimum speed for full turning
        
        # AI Assist
        self.ai_grip_multiplier = 1.0 # Can be increased for AI to prevent sliding

        # Boost state
        self.boost_active = False
        self.boost_fuel = 100.0
        self.boost_regen_rate = 0.2
        self.boost_use_rate = 1.5

        # Visual
        self.sprite = sprite
        self.is_player = is_player

        # Car dimensions for proper top-down proportions (5:8 width:height ratio)
        # Cars are longer than they are wide when viewed from above
        self.width = 100   # Car width (side to side)
        self.height = 160  # Car length (front to back)
        self.collision_rect = pg.Rect(0, 0, self.width, self.height)

        # Race state
        self.current_waypoint = 0
        self.lap = 0
        self.finished = False
        self.missed_checkpoint = False

        # Track state
        self.current_surface = 'asphalt'
        self.surface_modifier = 1.0  # Speed multiplier
        self.on_road = True
        self.off_track_time = 0.0  # Time spent off-track
        self.on_start_line = False
        self.on_finish_line = False
        self.crossed_finish = False  # Crossed finish line this lap

        # Vehicle Condition (0.0 = New, 100.0 = Broken)
        self.wear_tires = 0.0
        self.wear_brakes = 0.0
        self.wear_suspension = 0.0
        
        # Engine State
        self.engine_temp = TEMP_AMBIENT
        self.rpm = 0.0 # 0.0 to 1.0
        self.current_gear = 1
        self.gear_ratios = [0.0, 0.15, 0.30, 0.45, 0.60, 0.75, 1.0] # Matches audio logic
        self.is_overheated = False

        # Audio
        self.audio_controller = None

        # Particles
        self.tire_smoke_emitter = TireSmokeEmitter(self.x, self.y, 'asphalt')
        self.spark_emitter = SparkEmitter(self.x, self.y)
        self.is_drifting = False
        self.drift_coyote_time = 0.0
        self.screen_kick = 0.0
        self.pulse_timer = 0.0
        
        # Ghost Mode (Safe Respawn)
        self.is_ghost = False
        self.ghost_timer = 0.0

        self._validate_attributes()

    def _validate_attributes(self):
        """Debug function to check for critical missing attributes."""
        required_attrs = ['x', 'y', 'angle', 'velocity']
        missing = [attr for attr in required_attrs if not hasattr(self, attr)]
        if missing:
            print(f"[CAR DEBUG] Missing attributes: {missing}")
            print(f"[CAR DEBUG] dir(self): {dir(self)}")

    @property
    def position(self):
        """Returns the car's position as a Vector2."""
        return pg.Vector2(self.x, self.y)

    @position.setter
    def position(self, value):
        """Sets the car's position from a Vector2."""
        self.x = value.x
        self.y = value.y

    def init_audio(self, audio_manager):
        """Initialize the audio controller."""
        self.audio_controller = CarAudioController(self, audio_manager)

    def update_audio(self, dt, listener_car):
        """Update audio controller."""
        if self.audio_controller:
            self.audio_controller.update(dt, listener_car)

    def cleanup(self):
        """Clean up audio resources."""
        if self.audio_controller:
            self.audio_controller.cleanup()

    def reset(self, pos: pg.Vector2, angle: float):
        """
        Resets the car's state to a given position and angle.
        Useful for race restarts or manual resets.
        """
        self.position = pos
        self.angle = angle
        self.velocity = pg.Vector2(0.0, 0.0)
        self.angular_velocity = 0.0
        
        # Reset inputs
        self.throttle = 0.0
        self.steering = 0.0
        self.is_braking = 0.0
        
        # Reset race progress
        self.lap = 0
        self.current_waypoint = 0
        self.finished = False
        self.missed_checkpoint = False
        self.crossed_finish = False
        
        # Reset vehicle status
        self.engine_temp = TEMP_AMBIENT
        self.is_overheated = False
        
        # Reset timers and flags
        self.is_ghost = False
        self.ghost_timer = 0.0
        self.off_track_time = 0.0

    def respawn_at_last_checkpoint(self):
        """
        Resets the car to the last valid checkpoint position.
        Used for 'Back to Track' functionality.
        """
        self.position = pg.Vector2(self.last_checkpoint_pos)
        self.velocity = pg.Vector2(0, 0)
        self.angular_velocity = 0.0
        self.angle = self.last_asphalt_angle # Best guess for orientation
        self.throttle = 0.0
        self.steering = 0.0
        self.is_braking = 0.0
        
        # Enable Ghost Mode briefly
        self.is_ghost = True
        self.ghost_timer = 2.0
        print(f"[CAR] Respawned at checkpoint: {self.position}")

    def get_speed_kmh(self):
        """Returns current speed in km/h for display."""
        # Tuned for visual realism with new physics scale
        return self.velocity.magnitude() * 15.0

    def update(self, dt, keys=None, tmx_map=None):
        """
        Update car physics for one frame.

        Args:
            dt: Delta time in seconds
            keys: Dict of pressed keys (for player input)
            tmx_map: TMXMap object for collision detection (optional)
        """
        # print(f"[CAR] Update: dt={dt:.4f}, IsPlayer={self.is_player}, Finished={self.finished}, Pos={self.position}, Vel={self.velocity.length():.2f}")
        if self.finished:
            return

        if self.is_player and keys:
            self._handle_input(keys)
            # DEBUG: Log player car's state for the first 100 frames
            if pg.time.get_ticks() < 5000: # Approx 100 frames at 60fps
                print(f"[PLAYER_CAR DEBUG] Pos: {self.position}, Vel: {self.velocity.length():.2f}")
        
        # Apply controls to physics
        self._apply_controls(dt)

        # Update vehicle condition and thermodynamics
        self._update_status(dt)
        
        # Update Ghost Mode
        if self.is_ghost:
            self.ghost_timer -= dt
            if self.ghost_timer <= 0:
                self.is_ghost = False
                self.ghost_timer = 0.0

        # Update position based on velocity
        self._update_physics(dt)

        # Check collision with track boundaries using TMX
        if tmx_map:
            self._update_surface_physics(tmx_map)

    def _handle_input(self, keys):
        """Process player input and update control states."""
        self.throttle = 0.0
        self.is_braking = 0.0
        self.steering = 0.0

        if (keys.get(pg.K_UP) or keys.get(pg.K_w)):
            self.throttle = 1.0
        elif (keys.get(pg.K_DOWN) or keys.get(pg.K_s)):
            self.is_braking = 1.0

        if (keys.get(pg.K_LEFT) or keys.get(pg.K_a)):
            self.steering = -1.0
        elif (keys.get(pg.K_RIGHT) or keys.get(pg.K_d)):
            self.steering = 1.0
        
        if keys.get(pg.K_SPACE) and self.boost_power > 0:
            self.boost_active = True
        else:
            self.boost_active = False

    def _apply_controls(self, dt):
        """
        Apply control inputs (Drifting, Boost, Particles).
        Movement logic is now handled in _update_physics for stability.
        """
        # --- Drifting ---
        if self.velocity.magnitude() > 3.0 and self.steering != 0 and self.is_braking:
            self.is_drifting = True
            self.drift_coyote_time = 0.2
        
        if self.drift_coyote_time > 0:
            self.drift_coyote_time -= dt
            self.tire_smoke_emitter.start_emission(30)
            self.spark_emitter.start_emission(10)
        else:
            self.is_drifting = False
            self.tire_smoke_emitter.stop_emission()
            self.spark_emitter.stop_emission()

        # --- Boost ---
        if self.boost_active:
            self.boost_fuel -= self.boost_use_rate
            if self.boost_fuel <= 0:
                self.boost_fuel = 0
                self.boost_active = False
        else:
            if self.boost_fuel < 100:
                self.boost_fuel = min(100, self.boost_fuel + self.boost_regen_rate)

    def _update_physics(self, dt):
        """
        Update position and apply physics using Robust Newtonian Logic.
        """
        # 1. GET CURRENT STATE (Signed Scalar Speed)
        angle_rad = math.radians(self.angle)
        heading = pg.Vector2(math.cos(angle_rad), math.sin(angle_rad))
        # Project current velocity onto car's heading to get signed speed
        current_speed = self.velocity.dot(heading)
        
        # 2. CONVERT SPEED TO SI UNITS (needed for debug and physics)
        # Calculate absolute speed first
        abs_speed = abs(current_speed)
        # Current speed in m/s for physics calculations
        current_speed_ms = self.physics_engine.from_game_units(abs_speed, is_speed=True)
        speed_sign = 1.0 if current_speed >= 0 else -1.0
        
        # --- DEBUG PROBE (Updated for new physics system) ---
        # Print only once per second (assuming 60 FPS) to avoid console spam
        if getattr(self, 'debug_timer', 0) % 60 == 0:
            print(f"[PHYSICS DEBUG] Speed: {current_speed:.2f} units ({current_speed_ms:.2f} m/s)")
            print(f" -> Inputs: Throttle={self.throttle:.2f}, Brake={self.is_braking:.2f}")
            
            # Show current forces being applied (dynamic, calculated per-frame)
            if self.throttle > 0 and self.has_engine:
                # Calculate current acceleration force
                speed_ratio = abs(current_speed_ms) / max(1.0, self.physics_engine.calculate_top_speed_ms())
                rpm_factor = 0.6 + (speed_ratio * 0.4)
                accel_ms2 = self.physics_engine.calculate_acceleration(
                    current_speed_ms, self.throttle, self.current_surface, rpm_factor
                )
                print(f" -> Acceleration: {accel_ms2:.2f} m/s² (RPM factor: {rpm_factor:.2f})")
            
            if self.is_braking > 0:
                # Calculate current braking force
                brake_ms2 = self.physics_engine.calculate_braking_force(
                    current_speed_ms, self.is_braking, 
                    self.braking_efficiency, self.current_surface
                )
                print(f" -> Braking: {abs(brake_ms2):.2f} m/s² (efficiency: {self.braking_efficiency:.2f})")
            
            print(f" -> Limits: TopSpeed={self.max_kmh:.1f}km/h ({self.max_speed_units:.2f} units)")
            print(f" -> Surface: {self.current_surface} (modifier: {self.surface_modifier:.2f})")
            print(f" -> Power: {self.hp:.0f}HP, Mass: {self.mass:.0f}kg, P/W: {self.power_to_weight:.1f}HP/ton")

        self.debug_timer = getattr(self, 'debug_timer', 0) + 1
        
        # 3. APPLY DYNAMIC PHYSICS (Data-Driven)
        
        # --- BRAKING (Priority over Throttle) ---
        if self.is_braking > 0:
            if abs(current_speed_ms) > 0.1:
                # Apply realistic braking using physics engine
                decel_ms2 = self.physics_engine.calculate_braking_force(
                    current_speed_ms,
                    brake_input=self.is_braking,
                    brake_efficiency=self.braking_efficiency,
                    surface=self.current_surface
                )
                
                # Convert to game units and apply
                decel_game = self.physics_engine.to_game_units(decel_ms2, is_speed=False)
                current_speed += decel_game * dt
                
                # Don't overshoot zero
                if abs(current_speed) < 0.1:
                    current_speed = 0
                
                self.screen_kick = -2
            else:
                # Stopped -> Reverse
                # Only reverse if throttle is NOT pressed
                if self.throttle == 0:
                    # Use physics engine for reverse (50% throttle equivalent)
                    accel_ms2 = self.physics_engine.calculate_acceleration(
                        0.5,  # Low speed for reverse
                        throttle=self.is_braking * 0.5,
                        surface=self.current_surface,
                        rpm_factor=0.6
                    )
                    accel_game = self.physics_engine.to_game_units(accel_ms2, is_speed=False)
                    # Apply tuning multiplier to reverse as well
                    accel_game *= self.accel_multiplier
                    current_speed -= accel_game * dt  # Negative for reverse

        # --- ACCELERATION ---
        elif self.throttle > 0:
            # Check if engine is present
            if not self.has_engine:
                # No engine = no acceleration
                print("[CAR DEBUG] No engine - cannot accelerate!")
                pass
            else:
                # Calculate RPM factor (simplified - based on speed)
                # At low speeds, engine is less efficient (high gear ratio)
                speed_ratio = abs(current_speed_ms) / max(1.0, self.physics_engine.calculate_top_speed_ms())
                rpm_factor = 0.6 + (speed_ratio * 0.4)  # Range: 0.6 to 1.0
                
                # Apply realistic acceleration using physics engine
                accel_ms2 = self.physics_engine.calculate_acceleration(
                    current_speed_ms,
                    throttle=self.throttle,
                    surface=self.current_surface,
                    rpm_factor=rpm_factor
                )
                
                # Convert to game units and apply
                accel_game = self.physics_engine.to_game_units(accel_ms2, is_speed=False)
                
                # Apply acceleration multiplier for tuned gameplay feel
                accel_game *= self.accel_multiplier
                
                # DIAGNOSTIC OUTPUT
                if getattr(self, 'diag_counter', 0) % 60 == 0:  # Every second
                    print(f"[CAR ACCEL DEBUG]")
                    print(f"  HP: {self.hp}, Mass: {self.mass}kg")
                    print(f"  Speed: {current_speed:.2f} game_units = {current_speed_ms:.2f} m/s")
                    print(f"  Accel (raw): {accel_ms2:.4f} m/s² → {accel_game / self.accel_multiplier:.4f} game_units")
                    print(f"  Accel (tuned {self.accel_multiplier}x): {accel_game:.4f} game_units")
                    print(f"  Throttle: {self.throttle:.2f}, RPM factor: {rpm_factor:.2f}")
                    print(f"  Before: {current_speed:.4f}, After: {current_speed + accel_game * dt:.4f}")
                
                self.diag_counter = getattr(self, 'diag_counter', 0) + 1
                
                current_speed += accel_game * dt
                self.screen_kick = 2
                
                # Boost multiplier if active
                if self.boost_active and self.boost_fuel > 0:
                    boost_mult = 1.0 + (self.boost_power / 100.0)
                    current_speed += accel_game * (boost_mult - 1.0) * dt
        
        # Drag is already included in physics engine calculations
        # (calculate_acceleration includes rolling resistance and air drag)

        # --- CLAMP SPEED TO LIMITS ---
        # Max speed is data-driven from JSON (top_speed_kmh converted to game units)
        limit_fwd = self.max_speed_units
        limit_rev = -limit_fwd * 0.35  # 35% of max forward speed for reverse
        
        # Clamp Forward Speed
        if current_speed > limit_fwd:
            current_speed = limit_fwd
        # Clamp Reverse Speed
        if current_speed < limit_rev:
            current_speed = limit_rev
            
        # 3. HANDLE STEERING (Rotational Speed Limit)
        # Turn rate limit (Degrees per second)
        turn_speed_limit = 120.0
        # abs_speed was already calculated above
        
        if abs_speed > 10.0: # Use a lower threshold for speed factor
            turn_speed_limit = 60.0 # Stiffer at high speed
            
        rotation_amount = self.steering * turn_speed_limit * dt
        self.angle += rotation_amount
        
        # 4. RECONSTRUCT VELOCITY VECTOR
        # Update heading based on new angle
        angle_rad = math.radians(self.angle)
        heading = pg.Vector2(math.cos(angle_rad), math.sin(angle_rad))
        
        target_velocity = heading * current_speed
            
        # Apply Lateral Grip (Mass-dependent drift handling)
        # Heavier cars have more momentum, making them harder to turn but more stable
        
        # For very low speeds (starting from 0), use direct velocity application
        # This prevents lerp() issues when velocity magnitude is near zero
        if abs_speed < 0.05:
            # Near-zero speed: Direct application for responsive starting
            if abs(current_speed) > 0.001:
                # Apply the calculated velocity directly
                self.velocity = target_velocity
            else:
                # Truly stopped
                self.velocity = pg.Vector2(0, 0)
        else:
            # Normal speed: Use grip-based lerp for realistic handling
            # Base grip from parts/setup
            base_grip = 0.85 * self.grip_efficiency * self.ai_grip_multiplier
            
            # Mass effect: Heavier cars are less agile (more inertia)
            # Lighter cars can change direction more quickly
            mass_agility_factor = 1.0 - ((self.mass - 1000.0) / 4000.0)  # Range ~0.8 to 1.1
            mass_agility_factor = max(0.7, min(1.2, mass_agility_factor))
            
            GRIP_FACTOR = base_grip * mass_agility_factor
            
            # Drifting reduces grip significantly
            if self.is_drifting:
                GRIP_FACTOR = 0.15  # Low grip during drift
            
            # Lerp velocity towards heading (grip simulation)
            self.velocity = self.velocity.lerp(target_velocity, GRIP_FACTOR)
            
        # Store last known good position
        if self.current_surface == 'asphalt':
            self.last_asphalt_pos.x = self.x
            self.last_asphalt_pos.y = self.y
            self.last_asphalt_angle = self.angle

        # Update position
        self.x += self.velocity.x * dt * 60 # Scale for visual speed
        self.y += self.velocity.y * dt * 60
        
        # Update collision rect
        self.collision_rect.center = (int(self.x), int(self.y))

        # Update collision rect
        self.collision_rect.center = (int(self.x), int(self.y))

        # Update particle emitters position
        self.tire_smoke_emitter.pos.x = self.x
        self.tire_smoke_emitter.pos.y = self.y
        self.spark_emitter.pos.x = self.x
        self.spark_emitter.pos.y = self.y
        self.tire_smoke_emitter.update(dt)
        self.spark_emitter.update(dt)

        # Update screen kick
        self.screen_kick *= 0.9 # Dampen the kick over time

        # Update pulse timer for CA-Evolution effect
        self.pulse_timer += dt

    def _check_tmx_collision(self, tmx_map):
        """
        Legacy collision check. The new model uses _update_surface_physics.
        This can be kept for AI or specific collision objects if needed, but is
        currently disabled for the player to allow driving anywhere.
        """
        pass


    def _update_surface_physics(self, tmx_map):
        """
        Determine surface type under the car and apply physics modifiers.
        """
        print(f"[CAR._update_surface_physics] Initial Surface: {self.current_surface}, Modifier: {self.surface_modifier:.2f}, Friction: {self.friction:.4f}")
        # Get tile GID at the car's center
        gid = tmx_map.get_tile_at_pixel(self.x, self.y)

        # Determine surface type from GID
        # This assumes specific GID ranges for surfaces.
        # TODO: Move this to a more data-driven system (e.g., TMX properties)
        new_surface = 'grass' # Default to grass
        if gid in tmx_map.ROAD_TILES:
            new_surface = 'asphalt'
        elif gid in tmx_map.SHOULDER_TILES: # Assuming SHOULDER_TILES is defined in tmx_loader
            new_surface = 'offroad'

        # Smoothly transition modifiers
        target_modifier = 1.0
        target_friction = self.base_friction

        if new_surface == 'asphalt':
            target_modifier = 1.0
            target_friction = 0.995 # Standard friction
            self.tire_smoke_emitter.set_surface('asphalt')
        elif new_surface == 'offroad':
            target_modifier = 0.7
            target_friction = 0.985 # More friction
            self.tire_smoke_emitter.set_surface('dirt')
        elif new_surface == 'grass':
            target_modifier = 0.3
            target_friction = 0.97 # High friction, low traction (sliding)
            self.tire_smoke_emitter.set_surface('grass')

        # LERP for smooth transition
        self.surface_modifier += (target_modifier - self.surface_modifier) * 0.1
        self.friction += (target_friction - self.friction) * 0.1
        self.current_surface = new_surface
        print(f"[CAR._update_surface_physics] New Surface: {new_surface}, Target Modifier: {target_modifier:.2f}, Current Modifier: {self.surface_modifier:.2f}, Target Friction: {target_friction:.4f}, Current Friction: {self.friction:.4f}")
        
        # Update on_road status for other game logic
        self.on_road = (new_surface == 'asphalt')

        # Check start/finish line detection
        self.on_start_line = tmx_map.is_on_start_line(self.x, self.y)
        was_on_finish = self.on_finish_line
        self.on_finish_line = tmx_map.is_on_finish_line(self.x, self.y)

        # Detect crossing finish line (entering the tile)
        if self.on_finish_line and not was_on_finish:
            self.crossed_finish = True

    def reset_to_asphalt(self, tmx_map=None):
        """
        Resets the car to the nearest 'asphalt' tile by searching outwards
        from the car's current position.
        """
        if tmx_map and tmx_map.road_layer:
            start_tx = int(self.x) // tmx_map.tile_width
            start_ty = int(self.y) // tmx_map.tile_height

            max_search_radius = 20  # Search up to 20 tiles away
            for r in range(max_search_radius):
                for i in range(-r, r + 1):
                    # Check top/bottom edges of search square
                    if tmx_map.is_on_road((start_tx + i) * tmx_map.tile_width, (start_ty - r) * tmx_map.tile_height):
                        self.x = (start_tx + i + 0.5) * tmx_map.tile_width
                        self.y = (start_ty - r + 0.5) * tmx_map.tile_height
                        self.velocity = pg.Vector2(0, 0)
                        print(f"[CAR] Reset to asphalt at tile ({start_tx+i}, {start_ty-r})")
                        return
                    if tmx_map.is_on_road((start_tx + i) * tmx_map.tile_width, (start_ty + r) * tmx_map.tile_height):
                        self.x = (start_tx + i + 0.5) * tmx_map.tile_width
                        self.y = (start_ty + r + 0.5) * tmx_map.tile_height
                        self.velocity = pg.Vector2(0, 0)
                        print(f"[CAR] Reset to asphalt at tile ({start_tx+i}, {start_ty+r})")
                        return
                    # Check left/right edges
                    if tmx_map.is_on_road((start_tx - r) * tmx_map.tile_width, (start_ty + i) * tmx_map.tile_height):
                        self.x = (start_tx - r + 0.5) * tmx_map.tile_width
                        self.y = (start_ty + i + 0.5) * tmx_map.tile_height
                        self.velocity = pg.Vector2(0, 0)
                        print(f"[CAR] Reset to asphalt at tile ({start_tx-r}, {start_ty+i})")
                        return
                    if tmx_map.is_on_road((start_tx + r) * tmx_map.tile_width, (start_ty + i) * tmx_map.tile_height):
                        self.x = (start_tx + r + 0.5) * tmx_map.tile_width
                        self.y = (start_ty + i + 0.5) * tmx_map.tile_height
                        self.velocity = pg.Vector2(0, 0)
                        print(f"[CAR] Reset to asphalt at tile ({start_tx+r}, {start_ty+i})")
                        return

        # Fallback to last known good position
        self.x = self.last_asphalt_pos.x
        self.y = self.last_asphalt_pos.y
        self.angle = self.last_asphalt_angle
        self.velocity = pg.Vector2(0, 0)
        self.angular_velocity = 0.0
        print(f"[CAR] Reset (fallback) to {self.x}, {self.y}")

    def check_car_collision(self, other_car):
        """Check and respond to collision with another car using pixel-perfect detection."""
        # Ghost Mode check
        if self.is_ghost or other_car.is_ghost:
            return False
        
        # Use pixel-perfect collision system
        collision_system = get_collision_system()
        collision_detected, overlap_point = collision_system.check_car_collision(self, other_car)
        
        if collision_detected:
            # Get collision normal (direction to separate cars)
            normal = collision_system.get_collision_normal(self, other_car)
            
            if normal:
                # Calculate separation distance based on car sizes
                self_radius = (self.width + self.height) / 4
                other_radius = (other_car.width + other_car.height) / 4
                separation = (self_radius + other_radius) * 0.3  # Smaller push for pixel-perfect
                
                # Push cars apart along the normal
                push_x = normal.x * separation * 0.5
                push_y = normal.y * separation * 0.5
                
                self.x += push_x
                self.y += push_y
                other_car.x -= push_x
                other_car.y -= push_y
                
                # Reduce velocity on collision
                if self.velocity.magnitude() > 0:
                    self.velocity *= 0.7
                if other_car.velocity.magnitude() > 0:
                    other_car.velocity *= 0.7
                
                # Play crash sound
                if self.audio_controller:
                    self.audio_controller.play_crash_sound(severity=0.7)
                
                # Trigger opponent audio too if exists
                if other_car.audio_controller:
                    other_car.audio_controller.play_crash_sound(severity=0.7)
                
                return True
        
        return False

    def check_waypoint(self, waypoints, lap_total, is_circuit=True):
        """
        Check if car has passed through the next waypoint.

        Args:
            waypoints: List of waypoint positions
            lap_total: Total laps required (only used in circuit mode)
            is_circuit: If True, waypoints loop and laps are counted.
                       If False, waypoints don't loop (point-to-point race).
        """
        if self.finished:
            return False

        if not waypoints or self.current_waypoint >= len(waypoints):
            return False

        wp = waypoints[self.current_waypoint]
        dist = math.sqrt((self.x - wp.x)**2 + (self.y - wp.y)**2)
 
        # Strict waypoint checking
        # Radius increased to 300 (30px) to avoid missing points at speed
        if dist < 300:
            self.last_checkpoint_pos = pg.Vector2(wp.x, wp.y)
            self.current_waypoint += 1
            self.missed_checkpoint = False

            if self.current_waypoint >= len(waypoints):
                if is_circuit:
                    # Circuit race: Wait for finish line to increment lap
                    pass
                else:
                    # Point-to-point: Finish immediately
                    return True
        return False

    def check_finish_line(self, lap_total, require_waypoints=True, min_waypoints=3):
        """
        Check if car crossed the finish line and count laps.

        Args:
            lap_total: Total laps required to finish
            require_waypoints: If True, requires passing some waypoints before lap counts
            min_waypoints: Minimum waypoints to pass before finish line counts

        Returns:
            True if a new lap was completed
        """
        if self.finished:
            return False

        if self.crossed_finish:
            self.crossed_finish = False  # Reset flag

            # Validate lap (prevent cheating by crossing finish line without completing track)
            if require_waypoints and self.current_waypoint < min_waypoints and not self.missed_checkpoint:
                return False  # Haven't passed enough waypoints
 
            # Complete the lap
            self.current_waypoint = 0
            self.lap += 1

            if self.lap >= lap_total:
                self.finished = True

            return True

        return False

    def _update_status(self, dt):
        """Update car mechanical status (temperature, wear)."""
        speed_ratio = min(1.0, self.velocity.magnitude() / 10.0)
        
        # 1. Update Gear and RPM (Simplified Logic matching Audio)
        new_gear = 1
        for i in range(len(self.gear_ratios) - 1):
            if speed_ratio > self.gear_ratios[i]:
                new_gear = i + 1
        self.current_gear = new_gear
        
        # RPM Calc
        lower_bound = self.gear_ratios[self.current_gear - 1] if self.current_gear > 0 else 0
        upper_bound = self.gear_ratios[self.current_gear] if self.current_gear < len(self.gear_ratios) else 1.0
        gear_range = max(0.01, upper_bound - lower_bound)
        rel_speed = max(0, speed_ratio - lower_bound)
        self.rpm = max(0.2, min(1.0, rel_speed / gear_range))
        
        # 2. Engine Temperature Logic
        # Heat generation
        heat_gen = 0.0
        if self.throttle > 0:
            # More heat at high RPM and high gear (load)
            load_factor = (self.current_gear / 6.0) + 0.5
            # Increased heat generation to ensure it can overcome cooling at high load
            heat_gen = self.rpm * self.throttle * 25.0 * load_factor * dt
        
        # Cooling (Airflow + Radiator)
        # Cooling increases significantly with speed (airflow)
        # Scaled by cooling_efficiency (better parts = better cooling)
        airflow_cooling = (self.velocity.magnitude() / 10.0) * 12.0 * dt * self.cooling_efficiency
        passive_cooling = 2.0 * dt * self.cooling_efficiency # Base radiator cooling
        
        # Engine braking cooling bonus (when high RPM but no throttle)
        if self.throttle == 0 and self.rpm > 0.5:
            passive_cooling += 3.0 * dt * self.cooling_efficiency
            
        target_temp = TEMP_AMBIENT
        temp_change = heat_gen - airflow_cooling - passive_cooling
        
        # Apply change but pull towards ambient if engine off/cold
        self.engine_temp += temp_change
        
        # Natural convection to ambient
        if self.engine_temp > TEMP_AMBIENT:
             self.engine_temp -= (self.engine_temp - TEMP_AMBIENT) * 0.05 * dt
             
        self.engine_temp = max(TEMP_AMBIENT, self.engine_temp)

        # Check thresholds
        if self.engine_temp > TEMP_FAILURE:
             self.is_overheated = True
        elif self.engine_temp < TEMP_OPTIMAL:
             self.is_overheated = False

    def draw(self, screen, camera_offset=(0, 0)):
        """Draw the car on screen."""
        if not self.sprite:
            # Fallback: draw a simple rectangle
            surf = pg.Surface((self.width, self.height), pg.SRCALPHA)
            color = ACCENT_GREEN if self.is_player else ACCENT_RED
            pg.draw.rect(surf, color, (0, 0, self.width, self.height))
        else:
            # Pulsing effect for bio-mutations
            pulse = 1.0 + math.sin(self.pulse_timer * 5) * 0.05 # 5% size pulse
            pulse_width = int(self.width * pulse)
            pulse_height = int(self.height * pulse)

            # Scale sprite
            scaled = pg.transform.scale(self.sprite, (pulse_width, pulse_height))
            
            # Ghost Mode Visuals
            if self.is_ghost:
                # Blink effect
                alpha = 128
                if int(self.ghost_timer * 10) % 2 == 0:
                    alpha = 180
                scaled.set_alpha(alpha)
                
            surf = scaled

            # Rotate sprite first to handle all subsequent placements
            display_angle = -self.angle - 90
            rotated_car = pg.transform.rotate(surf, display_angle)

            # Evolution Glow
            if self.is_player:
                # Create a surface for the glow effect, slightly larger than the car
                glow_surface = pg.Surface((pulse_width + 20, pulse_height + 20), pg.SRCALPHA)
                # Blit the unrotated car sprite onto the center of the glow surface
                glow_surface.blit(scaled, (10, 10))
                
                # Now, rotate the entire glow surface just like the car
                rotated_glow = pg.transform.rotate(glow_surface, display_angle)
                
                # The center for the glow and the car should be the same
                center_pos = (int(self.x - camera_offset[0]), int(self.y - camera_offset[1]))
                glow_rect = rotated_glow.get_rect(center=center_pos)
                
                # Blit the rotated glow with additive blending for the effect
                screen.blit(rotated_glow, glow_rect, special_flags=pg.BLEND_RGB_ADD)

        # Apply screen kick for visual feedback on acceleration/braking
        kick_offset_x = math.cos(math.radians(self.angle)) * self.screen_kick
        kick_offset_y = math.sin(math.radians(self.angle)) * self.screen_kick
        
        # Final car position includes the kick offset
        final_center = (int(self.x - camera_offset[0] + kick_offset_x), int(self.y - camera_offset[1] + kick_offset_y))
        rect = rotated_car.get_rect(center=final_center)
        screen.blit(rotated_car, rect)

        # Draw particles
        self.tire_smoke_emitter.draw(screen, camera_offset)
        self.spark_emitter.draw(screen, camera_offset)

        # Draw boost effect behind the car
        if self.boost_active and self.is_player:
            angle_rad = math.radians(self.angle)
            # Position boost flames behind the car (half the car length + some offset)
            boost_offset = self.height // 2 + 10
            bx = self.x - math.cos(angle_rad) * boost_offset - camera_offset[0]
            by = self.y - math.sin(angle_rad) * boost_offset - camera_offset[1]
            pg.draw.circle(screen, (255, 150, 0), (int(bx), int(by)), 12)
            pg.draw.circle(screen, (255, 255, 100), (int(bx), int(by)), 6)
            # SUGGESTION: Play a "nitro boost" sound effect here

        # Draw overheat smoke
        if self.engine_temp > TEMP_WARNING:
            angle_rad = math.radians(self.angle)
            # Front of car
            smoke_offset = self.height // 2
            sx = self.x + math.cos(angle_rad) * smoke_offset - camera_offset[0]
            sy = self.y + math.sin(angle_rad) * smoke_offset - camera_offset[1]
            
            # Smoke severity
            severity = min(1.0, (self.engine_temp - TEMP_WARNING) / (TEMP_FAILURE - TEMP_WARNING))
            alpha = int(180 * severity)
            
            # Draw multiple smoke puffs for better visibility
            smoke_surf = pg.Surface((50, 50), pg.SRCALPHA)
            # Inner white core
            pg.draw.circle(smoke_surf, (220, 220, 220, alpha), (25, 25), 20 * severity + 5)
            # Outer grey halo
            pg.draw.circle(smoke_surf, (100, 100, 100, alpha // 2), (25, 25), 25 * severity + 5)
            
            screen.blit(smoke_surf, (int(sx - 25), int(sy - 25)))