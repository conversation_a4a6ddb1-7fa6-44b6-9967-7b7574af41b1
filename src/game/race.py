"""
Race Module
===========
Main race gameplay state handling the race loop, UI, and completion.
"""
import math
import pygame as pg
import os
import logging
from src.constants import (
    MAPS_DIR,
    ASSETS_DIR,
    ACCENT_GREEN,
    ACCENT_RED,
    ACCENT_BLUE,
    ACCENT_GOLD,
    TEXT_MAIN,
    TEXT_DIM,
    TEMP_AMBIENT,
    TEMP_OPTIMAL,
    TEMP_WARNING,
    TEMP_OVERHEAT,
    TEMP_FAILURE
)
from src.game.car import Car
from src.game.track_manager import TrackManager
from src.game.ai_driver import AIDriver
from src.core.assets import get_car_sprite, load_image
from src.core.tmx_loader import load_tmx
from src.core.game_data import (
    get_track_data,
    get_opponent_data,
    get_car_stats,
    get_car_size_class
)
from src.game.camera import Camera
from src.game.particles import TireSmokeEmitter, SparkEmitter
from src.logic.race_manager import NavigationManager
from src.ui.effects import ScreenFader


class Race:
    """
    Main race gameplay class.
    Manages the race loop, player car, AI opponent, collision detection,
    and race completion.
    """

    def __init__(self, app, player, track_id: str, on_complete_callback, 
                opponent_id: str = None, difficulty: str = "MEDIUM", 
                multiplayer: bool = False):
        """
        Initializes the Race.
        """
        self.app = app
        self.player = player
        
        # WALIDACJA I NORMALIZACJA
        from src.core.game_data import normalize_track_id
        original_track_id = track_id
        track_id = normalize_track_id(track_id)
        
        if original_track_id != track_id:
            logging.warning(f"[RACE] Normalized track_id: {original_track_id} → {track_id}")
        
        self.track_id = track_id
        self.opponent_id = opponent_id
        self.difficulty = difficulty
        self.on_complete_callback = on_complete_callback
        self.multiplayer = multiplayer

        # Load track data
        self.track_data = get_track_data(track_id)
        
        # KRYTYCZNA WALIDACJA
        if self.track_data is None:
            logging.error(f"[RACE] Track data not found for track_id: {track_id}")
            # Fallback to track_0 if data is missing, but log error
            from src.core.game_data import get_track_data as get_td
            self.track_data = get_td("track_0")
            if self.track_data is None:
                 raise ValueError(f"Critical: Default track_0 data missing!")
        
        logging.info(f"[RACE] Loaded track: {track_id} → {self.track_data.get('map_file')}")
        
        self.opponent_data = get_opponent_data(opponent_id)

        # Race settings
        self.total_laps = self.track_data.get('laps', 3)
        self.track_manager = TrackManager()
        self.start_pos = pg.Vector2(self.track_data.get('start_pos', [640, 400]))
        self.start_angle = self.track_data.get('start_angle', 0)

        # Determine race type: circuit (laps) vs point-to-point (one run)
        race_type = self.track_data.get('race_type', 'circuit')
        self.is_circuit = (race_type == 'circuit') or (self.total_laps > 1)

        # Load TMX map or fallback to image
        self.tmx_map = None
        self.track_image = None
        
        # Race Manager
        # ✅ FIX: Ensure nav_file matches the loaded track_id
        # If track_data specifies a nav_file, use it. Otherwise construct from track_id.
        nav_file = self.track_data.get('nav_file')
        if not nav_file:
             # Construct default nav filename: map_{track_id}_nav.json
             # Note: track_id is like "track_0", but nav files might use "map_0_nav.json"
             # We need to extract the number
             import re
             match = re.search(r'track_(\d+)', track_id)
             if match:
                 nav_file = f"map_{match.group(1)}_nav.json"
             else:
                 nav_file = "map_0_nav.json"

        nav_path = os.path.join(os.path.dirname(ASSETS_DIR), 'data', 'nav', nav_file)
        self.nav_manager = None
        if os.path.exists(nav_path):
            self.nav_manager = NavigationManager(nav_path)
            logging.info(f"[RACE] Loaded navigation: {nav_file}")
        else:
            logging.warning(f"[RACE] Navigation file not found: {nav_path}")

        self._load_track()

        # Calculate proper start angle based on first waypoint/checkpoint
        self.start_angle = self._calculate_start_angle_to_first_waypoint()

        # Create player car
        self.player_car = self._create_player_car()

        # Check critical components
        if not self.player_car.has_engine:
            self.state = 'NO_ENGINE'
            logging.warning("[RACE] Car has no engine! Preventing start.")
        else:
            self.state = 'COUNTDOWN'  # COUNTDOWN, RACING, PAUSED, FINISHED

        # Create AI opponent
        # self.ai_driver = self._create_ai_opponent() # Moved after _load_track

        # Initialize Audio
        self.player_car.init_audio(self.app.audio)
        # self.ai_driver.car.init_audio(self.app.audio) # Moved after AI creation

        # Race state
        # self.state is initialized above based on engine check
        self.countdown = 3.0
        self.race_time = 0.0
        self.paused = False

        # UI fonts
        self.hud_font = pg.font.SysFont('Consolas', 28, bold=True)
        self.big_font = pg.font.SysFont('Consolas', 72, bold=True)
        self.info_font = pg.font.SysFont('Consolas', 24)

        # Camera - initialize centered between both cars
        # self._init_camera() # Moved after AI creation

        # Results
        self.winner = None
        self.reward = 0

        # Particle Emitters
        self.particle_emitters = []

        # ✅ MULTIPLAYER FIX: Initialize remote cars list
        self.remote_cars = []  # List of Car objects for other players

        # Create AI opponent or placeholder for multiplayer
        if not self.multiplayer:
            self.ai_driver = self._create_ai_opponent()
            self.ai_driver.car.init_audio(self.app.audio)
        else:
            self.ai_driver = None # No AI in multiplayer
            logging.info("[RACE] Multiplayer mode - AI disabled, remote_cars initialized")
            
            # ✅ FIX: Spawn Position Logic for Multiplayer
            # Determine lane based on player ID or host status
            # This requires the player object to have a 'player_id' or 'is_host' attribute
            # If not available, we can use a hash of the player name or ID to determine lane
            
            # Default to left lane (-1)
            lane_side = -1
            
            # Try to determine lane from player attributes if available
            if hasattr(self.player, 'is_host'):
                # Host spawns on LEFT (-1), Guest on RIGHT (1)
                lane_side = -1 if self.player.is_host else 1
                logging.info(f"[RACE] Spawn lane determined by host status: {lane_side} (Host={self.player.is_host})")
            elif hasattr(self.player, 'player_id') and self.player.player_id:
                # Fallback: Simple hash to distribute players if host status unknown
                # This is less reliable than explicit host flag
                lane_side = -1 if hash(self.player.player_id) % 2 == 0 else 1
                logging.info(f"[RACE] Spawn lane determined by player_id hash: {lane_side}")
            
            # Apply lane offset
            offset_x, offset_y = self._calculate_lane_offset(lane_side)
            self.player_car.x = self.start_pos.x + offset_x
            self.player_car.y = self.start_pos.y + offset_y
            # Update position vector and collision rect
            self.player_car.position = pg.Vector2(self.player_car.x, self.player_car.y)
            self.player_car.collision_rect.center = (int(self.player_car.x), int(self.player_car.y))
            
            logging.info(f"[RACE] Multiplayer spawn: {self.player_car.position} (Lane: {lane_side})")

        print(f"DEBUG: Track waypoints count: {len(self.track_manager.waypoints)}")

        self._init_camera() # Initialize camera

    def _calculate_start_angle_to_first_waypoint(self):
        """
        Calculate the angle from start position to the first waypoint/checkpoint.
        This ensures cars face the correct direction at race start.

        Returns:
            float: Angle in degrees (0=right, 90=down, 180=left, -90=up)
        """
        # Try to get waypoints from different sources in priority order
        waypoints = None

        # 1. Try navigation manager first (most accurate)
        if self.nav_manager and hasattr(self.nav_manager, 'path') and len(self.nav_manager.path) > 0:
            waypoints = self.nav_manager.path
            logging.info(f"[RACE] Using navigation waypoints for start angle calculation ({len(waypoints)} points)")

        # 2. Try track manager waypoints
        elif len(self.track_manager.waypoints) > 0:
            waypoints = self.track_manager.waypoints
            logging.info(f"[RACE] Using track manager waypoints for start angle calculation ({len(waypoints)} points)")

        # 3. Try TMX racing line
        elif self.tmx_map and hasattr(self.tmx_map, 'racing_line') and self.tmx_map.racing_line:
            waypoints = [pg.Vector2(p) for p in self.tmx_map.racing_line]
            logging.info(f"[RACE] Using TMX racing line for start angle calculation ({len(waypoints)} points)")

        # If no waypoints available, use TMX auto-detection or fallback
        if not waypoints:
            if self.tmx_map:
                angle = self.tmx_map.get_start_angle()
                logging.info(f"[RACE] Using TMX auto-detected start angle: {angle}°")
                return angle
            else:
                logging.warning("[RACE] No waypoints or TMX data available, using default start angle: -90°")
                return -90  # Default: facing up

        # Calculate direction to first waypoint
        first_waypoint = waypoints[0]

        # Convert to Vector2 if needed
        if not isinstance(first_waypoint, pg.Vector2):
            first_waypoint = pg.Vector2(first_waypoint[0], first_waypoint[1])

        # Calculate direction vector from start position to first waypoint
        direction = first_waypoint - self.start_pos

        # Calculate angle in degrees
        angle = direction.angle_to(pg.Vector2(1, 0))  # Angle relative to right (positive X)

        logging.info(f"[RACE] Calculated start angle to first waypoint: {angle}° (start: {self.start_pos}, target: {first_waypoint})")

        return angle

    def _init_camera(self):
        """Initialize camera centered between both cars."""
        screen_size = self.app.screen.get_size()
        track_size = self._get_track_size()
        self.camera = Camera(screen_size, track_size)
        self.camera.set_target(self.player_car)

    def _get_track_size(self):
        """Get the track dimensions in pixels."""
        if self.tmx_map:
            return self.tmx_map.pixel_width, self.tmx_map.pixel_height
        elif self.track_image:
            return self.track_image.get_width(), self.track_image.get_height()
        return 1280, 720

    def _load_track(self):
        """Load track from TMX or PNG file."""
        map_file = self.track_data.get('map_file', 'map_0.tmx')
        map_path = os.path.join(MAPS_DIR, map_file) # Use the MAPS_DIR constant

        # Try TMX first
        if map_file.endswith('.tmx'):
            self.tmx_map = load_tmx(map_path)
            if self.tmx_map:
                self.track_image = self.tmx_map.rendered_surface
                if not self.nav_manager:
                    self.track_manager.load_waypoints(self.tmx_map, start_pos=self.start_pos)
                logging.info(f"[RACE] Loaded TMX map: {map_file}")
 
                # Override start position and angle from TMX if available
                if self.tmx_map.start_line_pos:
                    self.start_pos = pg.Vector2(self.tmx_map.start_line_pos)
                    logging.info(f"[RACE] Using TMX start position: {self.start_pos}")

                # Auto-detect start angle from track layout
                tmx_angle = self.tmx_map.get_start_angle()
                self.start_angle = tmx_angle
                logging.info(f"[RACE] Using TMX start angle: {self.start_angle}°")

                return

        # Fallback to PNG
        png_path = map_path.replace('.tmx', '.png')
        self.track_image = load_image(png_path, alpha=False)

        if not self.track_image:
            # Ultimate fallback: create a simple surface
            self.track_image = pg.Surface((1280, 720))
            self.track_image.fill((50, 50, 50))
            logging.warning("[RACE] Warning: Using fallback track surface")

    def _calculate_lane_offset(self, lane_side):
        """
        Calculate x,y offset for positioning cars side-by-side based on start angle.

        Args:
            lane_side: -1 for left lane, +1 for right lane

        Returns:
            (offset_x, offset_y) tuple
        """
        import math
        # The offset should be perpendicular to the driving direction
        # start_angle is the direction cars face (0=right, 90=down, -90=up)
        # Perpendicular is start_angle + 90
        perp_angle = math.radians(self.start_angle + 90)
        lane_offset = 60  # Half the distance between cars (120px separation for 100px wide cars)

        offset_x = math.cos(perp_angle) * lane_offset * lane_side
        offset_y = math.sin(perp_angle) * lane_offset * lane_side

        return (offset_x, offset_y)

    def _create_player_car(self) -> Car:
        """
        Create the player's car with proper stats.

        Returns:
            Car: The player's initialized car instance.
        """
        # Get mounted parts - handle both singleplayer and multiplayer
        mounted_parts = {}
        
        # Method 1: Try get_mounted_parts() method (if player has it)
        if hasattr(self.player, 'get_mounted_parts') and callable(self.player.get_mounted_parts):
            try:
                mounted_parts = self.player.get_mounted_parts(self.player.current_car)
                logging.info(f"[RACE] Using player.get_mounted_parts(): {mounted_parts}")
            except Exception as e:
                logging.warning(f"[RACE] get_mounted_parts() failed: {e}")
        
        # Method 2: Try garage attribute (singleplayer)
        elif hasattr(self.player, 'garage'):
            for car_item in self.player.garage:
                if isinstance(car_item, dict):
                    car_id = car_item.get('model_id')
                    if car_id == self.player.current_car:
                        mounted_parts = car_item.get('mounted_parts', {})
                        logging.info(f"[RACE] Using garage mounted_parts: {mounted_parts}")
                        break
        
        # Method 3: Fallback to empty (multiplayer default)
        else:
            logging.info(f"[RACE] No mounted_parts available, using defaults (multiplayer mode)")
            mounted_parts = {}

        # Calculate stats
        stats = get_car_stats(self.player.current_car, mounted_parts)

        # Get sprite
        sprite = get_car_sprite(self.app.assets, self.player.current_car)

        # Create car at start position (left lane)
        offset_x, offset_y = self._calculate_lane_offset(-1)  # -1 = left lane
        start_x = self.start_pos.x + offset_x
        start_y = self.start_pos.y + offset_y

        return Car(start_x, start_y, self.start_angle, stats, sprite, is_player=True)

    def _create_ai_opponent(self) -> AIDriver:
        """
        Create the AI opponent.

        Returns:
            AIDriver: The AI driver instance controlling the opponent car.
        """
        skill = self.opponent_data.get('skill', 1.0)

        # AI uses a random car from the same class
        player_class = get_car_size_class(self.player.current_car)
        ai_car_id = f"car_{5 if player_class == 'XS' else (11 if player_class == 'S' else 17)}"

        # Difficulty Scaling
        # Mechanical Stats (Better parts on higher difficulty)
        mech_mult = 1.0
        # Behavioral Stats (Reaction, aggression)
        skill_mod = 0.0
        
        if self.difficulty == "EASY":
            mech_mult = 0.8
            skill_mod = -0.2
        elif self.difficulty == "HARD":
            mech_mult = 1.2
            skill_mod = 0.2
            
        final_skill = max(0.1, min(1.5, skill + skill_mod))

        # Base stats for AI (scaled by skill & difficulty)
        ai_stats = {
            'max_speed': 180 * final_skill * mech_mult,
            'acceleration': 130 * final_skill * mech_mult,
            'braking': 90 * final_skill * mech_mult,
            'boost_power': 0,
            # Mechanical Efficiency (Cooling/Grip)
            'cooling_efficiency': 1.0 * mech_mult,
            'grip_efficiency': 1.0 * mech_mult
        }

        # Get sprite
        sprite = get_car_sprite(self.app.assets, ai_car_id)

        # Create car at start position (right lane)
        offset_x, offset_y = self._calculate_lane_offset(1)  # +1 = right lane
        start_x = self.start_pos.x + offset_x
        start_y = self.start_pos.y + offset_y

        ai_car = Car(start_x, start_y, self.start_angle, ai_stats, sprite, is_player=False)
 
        return AIDriver(ai_car, self.track_manager, self.nav_manager, self.difficulty, is_circuit=self.is_circuit, tmx_map=self.tmx_map)

    def update(self, events, dt):
        """Main race update loop."""
        # Handle events
        for event in events:
            if event.type == pg.KEYDOWN:
                if event.key == pg.K_ESCAPE:
                    if self.state == 'RACING':
                        self.state = 'PAUSED'
                    elif self.state == 'PAUSED':
                        self.state = 'RACING'
                    elif self.state == 'FINISHED':
                        self._finish_race()

                # Handle Quit in Pause Menu
                if self.state == 'PAUSED':
                    # Stop engine sounds when paused
                    if self.player_car and hasattr(self.player_car, 'audio_controller') and self.player_car.audio_controller:
                        self.player_car.audio_controller.stop_all()
                    # ✅ DEFENSIVE: Check ai_driver exists before accessing
                    if self.ai_driver and hasattr(self.ai_driver, 'car') and self.ai_driver.car and hasattr(self.ai_driver.car, 'audio_controller') and self.ai_driver.car.audio_controller:
                        self.ai_driver.car.audio_controller.stop_all()

                    if event.key == pg.K_RETURN or event.key == pg.K_q:
                        self.cleanup()
                        self.on_complete_callback()
                
                if event.key == pg.K_r and self.state in ('RACING', 'PAUSED'):
                    mods = pg.key.get_mods()
                    if mods & pg.KMOD_SHIFT:
                        self.restart_race()
                    else:
                        self.player_car.respawn_at_last_checkpoint()

                if event.key == pg.K_RETURN and self.state == 'FINISHED':
                    self._finish_race()

                # Debug: Toggle waypoint display (3 args: OFF -> SIMPLE -> ADVANCED)
                if event.key == pg.K_F1:
                    current_mode = getattr(self, 'debug_mode', 0)
                    self.debug_mode = (current_mode + 1) % 3
                    mode_names = {0: "OFF", 1: "SIMPLE", 2: "ADVANCED"}
                    print(f"[DEBUG] Waypoint display: {mode_names[self.debug_mode]}")

        if self.state == 'NO_ENGINE':
            self._update_no_engine(events)
        elif self.state == 'COUNTDOWN':
            self._update_countdown(dt)
        elif self.state == 'RACING':
            self._update_racing(dt)
        elif self.state == 'PAUSED':
            pass  # Do nothing while paused
        elif self.state == 'RESETTING':
            self.trigger_reset(self.player_car, dt)

    def _update_no_engine(self, events):
        """Handle input when race cannot start due to missing engine."""
        for event in events:
            if event.type == pg.KEYDOWN:
                if event.key in (pg.K_ESCAPE, pg.K_RETURN, pg.K_SPACE):
                    self.cleanup()
                    self.on_complete_callback()

    def _update_countdown(self, dt):
        """Handle countdown before race starts."""
        self.countdown -= dt

        # Allow player to rev engine during countdown
        keys = pg.key.get_pressed()
        key_dict = {
            pg.K_UP: keys[pg.K_UP],
            pg.K_DOWN: keys[pg.K_DOWN],
            pg.K_w: keys[pg.K_w],
            pg.K_s: keys[pg.K_s],
        }
        # Update just inputs for revving, not physics movement
        if keys[pg.K_UP] or keys[pg.K_w]:
             self.player_car.throttle = 1.0
        else:
             self.player_car.throttle = 0.0

        # Update audio
        if self.player_car and hasattr(self.player_car, 'update_audio'):
            self.player_car.update_audio(dt, self.player_car)
        # ✅ DEFENSIVE: Check ai_driver exists
        if self.ai_driver and hasattr(self.ai_driver, 'car') and self.ai_driver.car and hasattr(self.ai_driver.car, 'update_audio'):
            self.ai_driver.car.update_audio(dt, self.player_car)

        # Keep camera centered on both cars during countdown
        self._init_camera()

        if self.countdown <= 0:
            self.state = 'RACING'
            self.app.audio.play_sfx('ui_select')

    def _update_racing(self, dt):
        """Update race gameplay."""
        self.race_time += dt

        # Get pressed keys for player input
        keys = pg.key.get_pressed()
        key_dict = {
            pg.K_UP: keys[pg.K_UP],
            pg.K_DOWN: keys[pg.K_DOWN],
            pg.K_LEFT: keys[pg.K_LEFT],
            pg.K_RIGHT: keys[pg.K_RIGHT],
            pg.K_w: keys[pg.K_w],
            pg.K_s: keys[pg.K_s],
            pg.K_a: keys[pg.K_a],
            pg.K_d: keys[pg.K_d],
            pg.K_SPACE: keys[pg.K_SPACE]
        }

        # Update player car with TMX collision
        self.player_car.update(dt, key_dict, self.tmx_map)

        # Update AI or network opponents
        if self.ai_driver:
            self.ai_driver.update(dt, self.player_car)
            self.ai_driver.car.update(dt, tmx_map=self.tmx_map)
            
            # Update Audio
            if self.player_car and hasattr(self.player_car, 'update_audio'):
                self.player_car.update_audio(dt, self.player_car)
            if self.ai_driver.car and hasattr(self.ai_driver.car, 'update_audio'):
                self.ai_driver.car.update_audio(dt, self.player_car)

            # Check car-to-car collision
            if self.player_car:
                collision_intensity = self.player_car.check_car_collision(self.ai_driver.car)
                if collision_intensity > 200:
                    self.camera.start_shake(magnitude=collision_intensity / 40, duration=0.3)
        else:  # ✅ MULTIPLAYER FIX: Update remote cars
            # Update audio for player car
            if self.player_car and hasattr(self.player_car, 'update_audio'):
                self.player_car.update_audio(dt, self.player_car)
            
            # ✅ Update remote cars physics (no input, just interpolate)
            if hasattr(self, 'remote_cars'):
                for remote_car in self.remote_cars:
                    if remote_car:
                        # Update remote car audio
                        if hasattr(remote_car, 'update_audio'):
                            remote_car.update_audio(dt, self.player_car)
                        
                        # Check collision with player
                        if self.player_car:
                            collision_intensity = self.player_car.check_car_collision(remote_car)
                            if collision_intensity > 200:
                                self.camera.start_shake(magnitude=collision_intensity / 40, duration=0.3)


        # Check waypoints for tracking progress
        if self.track_manager.waypoints:
            self.player_car.check_waypoint(self.track_manager.waypoints, self.total_laps, is_circuit=self.is_circuit)
            if self.ai_driver:
                self.ai_driver.car.check_waypoint(self.track_manager.waypoints, self.total_laps, is_circuit=self.is_circuit)

        # Check finish line crossing (if TMX has finish line defined)
        if self.tmx_map and self.tmx_map.finish_line_pos:
            # Require passing most waypoints before finish counts
            min_wp = max(1, int(len(self.track_manager.waypoints) * 0.9))
 
            self.player_car.check_finish_line(self.total_laps, require_waypoints=True, min_waypoints=min_wp)
            if self.ai_driver:
                self.ai_driver.car.check_finish_line(self.total_laps, require_waypoints=True, min_waypoints=min_wp)
            
        # Update Rankings
        p_prog = self.track_manager.get_car_progress(self.player_car)
        
        # Calculate rank based on progress comparison
        current_rank = 1
        
        if self.ai_driver:
            # Singleplayer: Compare with AI
            ai_prog = self.track_manager.get_car_progress(self.ai_driver.car)
            if ai_prog > p_prog:
                current_rank += 1
        elif self.multiplayer and hasattr(self, 'remote_cars'):
            # Multiplayer: Compare with all remote cars
            for r_car in self.remote_cars:
                if r_car:
                    # Use track manager to get progress for remote car
                    # Note: Remote cars might not have all physics data, but x/y/lap/waypoint should be synced
                    r_prog = self.track_manager.get_car_progress(r_car)
                    if r_prog > p_prog:
                        current_rank += 1
        
        self.player_rank = current_rank

        # Update camera to follow player
        self.camera.update(dt)

        # Check for race completion
        # AI failsafe: If AI reached final waypoint in point-to-point, count as finished
        if not self.is_circuit and self.ai_driver and self.ai_driver.reached_final_waypoint:
            self.ai_driver.car.finished = True

        if self.player_car.finished or (self.ai_driver and self.ai_driver.car.finished):
            self._determine_winner()

        # Update particle emitters
        for emitter in self.particle_emitters:
            emitter.update(dt)

        # Refined Off-Track Logic (Safe Reset)
        if self.nav_manager:
            is_off_track = self.nav_manager.check_off_track(self.player_car.position / 10.0)
            
            if is_off_track:
                # Only reset if stuck (slow speed) or way too far
                # Allow cutting corners at speed
                if self.player_car.get_speed_kmh() < 15:
                    self.player_car.off_track_time += dt
                else:
                    self.player_car.off_track_time = 0
            else:
                self.player_car.off_track_time = 0
                
            if self.player_car.off_track_time > 2.0:
                self.state = 'RESETTING'
                self.app.fader.start_fade_out()
                self.player_car.off_track_time = 0

    def trigger_reset(self, car, dt):
        self.app.fader.update(dt)
        if self.app.fader.is_fully_faded():
            # Find the closest safe point on the track to reset the car
            closest_point, closest_point_idx = self.nav_manager.get_closest_point(car.position / 10.0)
            
            respawn_pos = closest_point * 10.0
            
            # ✅ DEFENSIVE: Ensure respawn point is not on top of the opponent
            opponent = None
            if self.ai_driver and hasattr(self.ai_driver, 'car') and self.ai_driver.car:
                opponent = self.ai_driver.car if car == self.player_car else self.player_car

            if opponent and respawn_pos.distance_to(opponent.position) < 150:
                closest_point_idx = (closest_point_idx + 8) % len(self.nav_manager.path)
                closest_point = self.nav_manager.path[closest_point_idx]
                respawn_pos = closest_point * 10.0
            
            # Determine the correct angle based on the track direction
            next_point_idx = (closest_point_idx + 5) % len(self.nav_manager.path)
            direction_vector = self.nav_manager.path[next_point_idx] - closest_point
            angle = -direction_vector.angle_to(pg.Vector2(1, 0))

            # Use the new comprehensive reset method
            car.reset(respawn_pos, angle)
            
            # Enable Ghost Mode for a safe re-entry
            car.is_ghost = True
            car.ghost_timer = 3.0
            
            self.app.fader.start_fade_in()

        if not self.app.fader.is_fading() and self.app.fader.alpha == 0:
            self.state = 'RACING'

    def restart_race(self):
        """✅ DEFENSIVE FIX: Resets the entire race to its initial state."""
        # Reset player car to its starting lane
        if self.player_car:
            offset_x, offset_y = self._calculate_lane_offset(-1)
            player_start_pos = self.start_pos + pg.Vector2(offset_x, offset_y)
            self.player_car.reset(player_start_pos, self.start_angle)

        # ✅ DEFENSIVE: Reset AI car only if in singleplayer
        if self.ai_driver and hasattr(self.ai_driver, 'car') and self.ai_driver.car:
            offset_x, offset_y = self._calculate_lane_offset(1)
            ai_start_pos = self.start_pos + pg.Vector2(offset_x, offset_y)
            self.ai_driver.car.reset(ai_start_pos, self.start_angle)
            if hasattr(self.ai_driver, 'reset'):
                self.ai_driver.reset()  # Also reset the AI's internal logic state

        # Reset race state
        self.race_time = 0.0
        self.winner = None
        self.state = 'COUNTDOWN'
        self.countdown = 3.0

    def _determine_winner(self):
        """Determine the winner and calculate rewards."""
        self.state = 'FINISHED'

        if self.ai_driver:
            if self.player_car.finished and not self.ai_driver.car.finished:
                self.winner = 'PLAYER'
            elif self.ai_driver.car.finished and not self.player_car.finished:
                self.winner = 'AI'
            elif self.player_car.lap > self.ai_driver.car.lap:
                self.winner = 'PLAYER'
            else:
                self.winner = 'AI'
        else:
            # Multiplayer or solo time trial logic
            self.winner = 'PLAYER' # Default to player for now in solo/multiplayer finish

        # Calculate reward
        if self.winner == 'PLAYER':
            base_reward = self.track_data.get('base_reward', 500)
            mult = self.opponent_data.get('reward_mult', 1.0)
            self.reward = int(base_reward * mult)
        else:
            self.reward = 0

    def cleanup(self):
        """✅ DEFENSIVE FIX: Stop all race audio with null checks."""
        # Clean up player car
        if self.player_car:
            try:
                self.player_car.cleanup()
            except Exception as e:
                logging.error(f"[RACE] Error cleaning up player car: {e}")
        
        # Clean up AI driver (only in singleplayer)
        if self.ai_driver and hasattr(self.ai_driver, 'car') and self.ai_driver.car:
            try:
                self.ai_driver.car.cleanup()
            except Exception as e:
                logging.error(f"[RACE] Error cleaning up AI car: {e}")
        
        # Clean up remote cars (multiplayer)
        if hasattr(self, 'remote_cars'):
            for car in self.remote_cars:
                if car:
                    try:
                        car.cleanup()
                    except Exception as e:
                        logging.error(f"[RACE] Error cleaning up remote car: {e}")

    def _finish_race(self):
        """Apply rewards and return to hub."""
        self.cleanup()
        
        if self.winner == 'PLAYER' and self.reward > 0:
            self.player.money += self.reward

        self.on_complete_callback()

    def draw(self, screen):
        """Main draw method - full-screen immersive racing view."""
        screen_w = screen.get_width()
        screen_h = screen.get_height()

        # Fill background with dark asphalt color
        screen.fill((35, 35, 40))

        # Draw track
        if self.track_image:
            # The camera's apply method could be used here for a more advanced setup,
            # but for now, we'll just use the offset.
            camera_offset = self.camera.get_offset()
            track_rect = self.track_image.get_rect()
            track_rect.x = -int(camera_offset.x)
            track_rect.y = -int(camera_offset.y)
            screen.blit(self.track_image, track_rect)

        # Draw cars
        camera_offset_tuple = (self.camera.get_offset().x, self.camera.get_offset().y)
        
        # ✅ DEFENSIVE: Draw AI car (singleplayer only)
        if self.ai_driver and hasattr(self.ai_driver, 'car') and self.ai_driver.car:
            try:
                self.ai_driver.car.draw(screen, camera_offset_tuple)
            except Exception as e:
                logging.error(f"[RACE] Error drawing AI car: {e}")
        
        # ✅ MULTIPLAYER FIX: Draw remote cars with proper checks and logging
        if hasattr(self, 'remote_cars') and self.remote_cars:
            for i, car in enumerate(self.remote_cars):
                if car:
                    try:
                        # ✅ FIX: Ensure sprite is valid before drawing
                        if car.sprite is None:
                            # Try to reload sprite if missing
                            from src.core.assets import get_car_sprite
                            # Assuming car has stats with name/model_id
                            car_model = car.stats.get('name', 'car_0') if hasattr(car, 'stats') else 'car_0'
                            car.sprite = get_car_sprite(self.app.assets, car_model)
                            if car.sprite:
                                logging.info(f"[RACE] Reloaded sprite for remote car {i}")
                        
                        car.draw(screen, camera_offset_tuple)
                        
                        # Debug logging (only log once per second to avoid spam)
                        if getattr(self, '_last_remote_car_log', 0) < self.race_time:
                            logging.info(f"[RACE] Drawing remote car {i} at ({car.x:.1f}, {car.y:.1f})")
                            self._last_remote_car_log = self.race_time + 1.0
                    except Exception as e:
                        logging.error(f"[RACE] Error drawing remote car {i}: {e}")

        # ✅ DEFENSIVE: Draw player car
        if self.player_car:
            try:
                self.player_car.draw(screen, camera_offset_tuple)
            except Exception as e:
                logging.error(f"[RACE] Error drawing player car: {e}")

        # Draw particles
        for emitter in self.particle_emitters:
            emitter.draw(screen, camera_offset_tuple)

        # Debug: Draw waypoints (enable with F1)
        # Map integer mode to string for TrackManager
        debug_mode = getattr(self, 'debug_mode', 0)
        if debug_mode > 0:
            target_wp = self.ai_driver.target_wp_index if self.ai_driver else 0
            # If no AI, use player's current waypoint for visualization
            if not self.ai_driver and self.player_car:
                 target_wp = self.player_car.current_waypoint
            
            mode_str = "SIMPLE" if debug_mode == 1 else "ADVANCED"
            self.track_manager.draw_debug(screen, self.camera, target_wp, mode=mode_str)
            
            if self.ai_driver:
                self.ai_driver.draw_debug(screen, self.camera)
            
            # Draw stats overlay in ADVANCED mode
            if debug_mode == 2:
                self._draw_debug_overlay(screen)

        # Draw minimal overlay HUD (corners only)
        self._draw_overlay_hud(screen)

        # Draw state-specific overlays
        if self.state == 'NO_ENGINE':
            self._draw_no_engine(screen)
        elif self.state == 'COUNTDOWN':
            self._draw_countdown(screen)
        elif self.state == 'PAUSED':
            self._draw_paused(screen)
        elif self.state == 'FINISHED':
            self._draw_results(screen)

        if self.state == 'RESETTING':
            self._draw_resetting(screen)

    def _draw_debug_overlay(self, screen):
        """Draw detailed debug stats (F1 x 2)."""
        font = pg.font.SysFont('Consolas', 16)
        
        lines = [
            f"FPS: {self.app.clock.get_fps():.1f}",
            f"Mode: ADVANCED DEBUG",
            f"Resolution: {self.tmx_map.tile_width}x{self.tmx_map.tile_height} (Scale x10.0)" if self.tmx_map else "No TMX Map",
        ]
        
        if self.player_car:
            p = self.player_car
            lines.extend([
                "--- PLAYER ---",
                f"Pos: ({p.x:.1f}, {p.y:.1f}) Angle: {p.angle:.1f}",
                f"Vel: ({p.velocity.x:.1f}, {p.velocity.y:.1f}) Kmh: {p.get_speed_kmh():.1f}",
                f"WP Index: {p.current_waypoint} / {len(self.track_manager.waypoints)}",
                f"Lap: {p.lap} / {self.total_laps}",
                f"Off-track Time: {p.off_track_time:.2f}s"
            ])
            
            # Show distance to current/next waypoint
            if self.track_manager.waypoints and p.current_waypoint < len(self.track_manager.waypoints):
                wp = self.track_manager.waypoints[p.current_waypoint]
                dist = p.position.distance_to(wp)
                lines.append(f"Dist to WP#{p.current_waypoint}: {dist:.1f}px")
                
        y = 10
        for line in lines:
            text = font.render(line, True, (0, 255, 0))
            pg.draw.rect(screen, (0, 0, 0), (5, y, text.get_width()+10, 20))
            screen.blit(text, (10, y))
            y += 20

    def _draw_resetting(self, screen: pg.Surface):
        if self.app.fader.is_fading():
            text = self.app.lang.get("out_of_track", "OUT OF TRACK")
            text_surf = self.big_font.render(text, True, ACCENT_RED)
            text_rect = text_surf.get_rect(center=(screen.get_width() // 2, screen.get_height() // 2))
            screen.blit(text_surf, text_rect)

    def _draw_no_engine(self, screen):
        """Draw error message when no engine is installed."""
        # Semi-transparent overlay
        overlay = pg.Surface(screen.get_size(), pg.SRCALPHA)
        overlay.fill((0, 0, 0, 200))
        screen.blit(overlay, (0, 0))

        cx = screen.get_width() // 2
        cy = screen.get_height() // 2

        # Error text
        text = self.app.lang.get("race_no_engine")
        text_surf = self.big_font.render(text, True, ACCENT_RED)
        text_rect = text_surf.get_rect(center=(cx, cy - 20))
        screen.blit(text_surf, text_rect)

        # Back instruction
        back_text = f"ENTER - {self.app.lang.get('menu_back')}"
        back_surf = self.info_font.render(back_text, True, TEXT_DIM)
        back_rect = back_surf.get_rect(center=(cx, cy + 40))
        screen.blit(back_surf, back_rect)

    def _draw_overlay_hud(self, screen):
        """Draw ultra-minimal overlay HUD - small corner indicators only."""
        screen_w = screen.get_width()
        screen_h = screen.get_height()

        # Use smaller fonts for minimal HUD
        small_font = pg.font.SysFont('Consolas', 18, bold=True)
        medium_font = pg.font.SysFont('Consolas', 24, bold=True)

        # === TOP-LEFT: Compact lap and position ===
        # Position indicator (P1 or P2)
        # Use calculated rank
        pos = getattr(self, 'player_rank', 1)

        pos_color = ACCENT_GREEN if pos == 1 else ACCENT_RED
        pos_text = f"P{pos}"
        lap_text = f"{self.player_car.lap + 1}/{self.total_laps}"

        pos_surf = medium_font.render(pos_text, True, pos_color)
        lap_surf = small_font.render(lap_text, True, TEXT_MAIN)

        # Compact panel
        panel_w = max(pos_surf.get_width(), lap_surf.get_width()) + 16
        panel_h = 50
        panel_surf = pg.Surface((panel_w, panel_h), pg.SRCALPHA)
        pg.draw.rect(panel_surf, (0, 0, 0, 100), (0, 0, panel_w, panel_h), border_radius=6)
        panel_surf.blit(pos_surf, (8, 4))
        panel_surf.blit(lap_surf, (8, 28))
        screen.blit(panel_surf, (8, 8))

        # === TOP-RIGHT: Compact time ===
        minutes = int(self.race_time) // 60
        seconds = int(self.race_time) % 60
        ms = int((self.race_time % 1) * 100)
        time_text = f"{minutes:02d}:{seconds:02d}.{ms:02d}"
        time_surf = small_font.render(time_text, True, TEXT_MAIN)

        time_w = time_surf.get_width() + 16
        time_h = 28
        time_panel = pg.Surface((time_w, time_h), pg.SRCALPHA)
        pg.draw.rect(time_panel, (0, 0, 0, 100), (0, 0, time_w, time_h), border_radius=6)
        time_panel.blit(time_surf, (8, 4))
        screen.blit(time_panel, (screen_w - time_w - 8, 8))

        # === BOTTOM-LEFT: Compact speed ===
        speed = int(self.player_car.get_speed_kmh())
        speed_text = f"{speed} km/h"
        speed_surf = medium_font.render(speed_text, True, ACCENT_GOLD)

        speed_w = speed_surf.get_width() + 16
        speed_h = 32
        speed_panel = pg.Surface((speed_w, speed_h), pg.SRCALPHA)
        pg.draw.rect(speed_panel, (0, 0, 0, 100), (0, 0, speed_w, speed_h), border_radius=6)
        speed_panel.blit(speed_surf, (8, 4))
        screen.blit(speed_panel, (8, screen_h - speed_h - 8))

        # === BOTTOM-LEFT (Stacked): Mechanical Status ===
        # Temperature Bar
        temp_w, temp_h = 100, 8
        temp_panel = pg.Surface((temp_w + 12, temp_h + 12), pg.SRCALPHA)
        pg.draw.rect(temp_panel, (0, 0, 0, 100), (0, 0, temp_w + 12, temp_h + 12), border_radius=4)
        
        # Temp Bar Background
        pg.draw.rect(temp_panel, (40, 40, 40), (6, 6, temp_w, temp_h), border_radius=2)
        
        # Temp Bar Fill
        temp_ratio = min(1.0, (self.player_car.engine_temp - TEMP_AMBIENT) / (TEMP_FAILURE - TEMP_AMBIENT))
        fill_w = int(temp_w * temp_ratio)
        
        # Color gradient based on temp
        if self.player_car.engine_temp < TEMP_OPTIMAL:
            temp_color = ACCENT_BLUE
        elif self.player_car.engine_temp < TEMP_WARNING:
            temp_color = ACCENT_GREEN
        elif self.player_car.engine_temp < TEMP_OVERHEAT:
            temp_color = ACCENT_GOLD
        else:
            temp_color = ACCENT_RED
            
        if fill_w > 0:
            pg.draw.rect(temp_panel, temp_color, (6, 6, fill_w, temp_h), border_radius=2)
            
        # Warning blink
        if self.player_car.engine_temp > TEMP_WARNING:
            if (pg.time.get_ticks() // 500) % 2 == 0:
                 pg.draw.rect(temp_panel, (255, 0, 0), (0, 0, temp_w + 12, temp_h + 12), 2, border_radius=4)

        screen.blit(temp_panel, (8, screen_h - speed_h - temp_h - 25))
        
        # Tire/Wear Indicators (Tiny dots)
        wear_panel = pg.Surface((60, 20), pg.SRCALPHA)
        # Tires
        tire_color = ACCENT_GREEN
        if self.player_car.wear_tires > 80: tire_color = ACCENT_RED
        elif self.player_car.wear_tires > 40: tire_color = ACCENT_GOLD
        pg.draw.circle(wear_panel, tire_color, (10, 10), 6)
        # Brakes
        brake_color = ACCENT_GREEN
        if self.player_car.wear_brakes > 80: brake_color = ACCENT_RED
        elif self.player_car.wear_brakes > 40: brake_color = ACCENT_GOLD
        pg.draw.circle(wear_panel, brake_color, (30, 10), 6)
        # Suspension
        susp_color = ACCENT_GREEN
        if self.player_car.wear_suspension > 80: susp_color = ACCENT_RED
        elif self.player_car.wear_suspension > 40: susp_color = ACCENT_GOLD
        pg.draw.circle(wear_panel, susp_color, (50, 10), 6)
        
        screen.blit(wear_panel, (8 + speed_w + 5, screen_h - 35))

        # === BOTTOM-RIGHT: Compact boost bar (only if has boost) ===
        if self.player_car.boost_power > 0:
            bar_w, bar_h = 80, 10
            boost_panel = pg.Surface((bar_w + 12, bar_h + 12), pg.SRCALPHA)
            pg.draw.rect(boost_panel, (0, 0, 0, 100), (0, 0, bar_w + 12, bar_h + 12), border_radius=4)

            # Bar background
            pg.draw.rect(boost_panel, (40, 40, 40), (6, 6, bar_w, bar_h), border_radius=3)

            # Bar fill
            fill_w = int(bar_w * (self.player_car.boost_fuel / 100))
            boost_color = (255, 150, 0) if self.player_car.boost_active else ACCENT_BLUE
            if fill_w > 0:
                pg.draw.rect(boost_panel, boost_color, (6, 6, fill_w, bar_h), border_radius=3)

            screen.blit(boost_panel, (screen_w - bar_w - 20, screen_h - bar_h - 20))

    def _draw_hud(self, screen):
        """Legacy HUD method - redirects to overlay HUD."""
        self._draw_overlay_hud(screen)

    def _draw_minimap(self, screen):
        """Draw a small minimap in the corner showing car positions."""
        # Minimap dimensions
        map_w, map_h = 150, 100
        map_x = screen.get_width() - map_w - 15
        map_y = 70

        # Create minimap surface
        minimap = pg.Surface((map_w, map_h), pg.SRCALPHA)
        pg.draw.rect(minimap, (0, 0, 0, 140), (0, 0, map_w, map_h), border_radius=6)
        pg.draw.rect(minimap, (80, 80, 80), (0, 0, map_w, map_h), 1, border_radius=6)

        # Calculate scale
        track_w, track_h = self._get_track_size()
        scale_x = (map_w - 20) / max(1, track_w)
        scale_y = (map_h - 20) / max(1, track_h)
        scale = min(scale_x, scale_y)

        # Draw player position
        px = int(self.player_car.x * scale) + 10
        py = int(self.player_car.y * scale) + 10
        pg.draw.circle(minimap, ACCENT_BLUE, (px, py), 4)

        # Draw AI position
        ax = int(self.ai_driver.car.x * scale) + 10
        ay = int(self.ai_driver.car.y * scale) + 10
        pg.draw.circle(minimap, ACCENT_RED, (ax, ay), 4)

        screen.blit(minimap, (map_x, map_y))

    def _draw_boost_bar_corner(self, screen):
        """Draw boost bar in bottom-right corner."""
        if self.player_car.boost_power <= 0:
            return

        bar_x = screen.get_width() - 170
        bar_y = screen.get_height() - 50
        bar_w = 150
        bar_h = 20

        # Background panel
        panel_surf = pg.Surface((bar_w + 20, bar_h + 30), pg.SRCALPHA)
        pg.draw.rect(panel_surf, (0, 0, 0, 140), panel_surf.get_rect(), border_radius=8)

        # Fill
        fill_w = int(bar_w * (self.player_car.boost_fuel / 100))
        boost_color = (255, 150, 0) if self.player_car.boost_active else ACCENT_BLUE
        if fill_w > 0:
            pg.draw.rect(panel_surf, boost_color, (10, 25, fill_w, bar_h), border_radius=4)

        # Border
        pg.draw.rect(panel_surf, TEXT_DIM, (10, 25, bar_w, bar_h), 1, border_radius=4)

        # Label
        boost_label = self.info_font.render("BOOST", True, TEXT_MAIN)
        panel_surf.blit(boost_label, (10, 5))

        screen.blit(panel_surf, (bar_x - 10, bar_y - 25))

    def _draw_countdown(self, screen):
        """Draw the countdown overlay."""
        # Semi-transparent overlay
        overlay = pg.Surface(screen.get_size(), pg.SRCALPHA)
        overlay.fill((0, 0, 0, 100))
        screen.blit(overlay, (0, 0))

        # Countdown number
        count = max(1, int(self.countdown) + 1)
        if self.countdown <= 0:
            text = "GO!"
            color = ACCENT_GREEN
        else:
            text = str(count)
            color = ACCENT_GOLD

        text_surf = self.big_font.render(text, True, color)
        text_rect = text_surf.get_rect(center=(screen.get_width() // 2, screen.get_height() // 2))
        screen.blit(text_surf, text_rect)

    def _draw_paused(self, screen):
        """Draw the pause overlay."""
        # Semi-transparent overlay
        overlay = pg.Surface(screen.get_size(), pg.SRCALPHA)
        overlay.fill((0, 0, 0, 180))
        screen.blit(overlay, (0, 0))

        # Paused text
        text = self.app.lang.get("race_paused")
        text_surf = self.big_font.render(text, True, TEXT_MAIN)
        text_rect = text_surf.get_rect(center=(screen.get_width() // 2, screen.get_height() // 2 - 50))
        screen.blit(text_surf, text_rect)

        # Instructions
        resume_text = f"ESC - {self.app.lang.get('race_resume')}"
        resume_surf = self.info_font.render(resume_text, True, TEXT_DIM)
        resume_rect = resume_surf.get_rect(center=(screen.get_width() // 2, screen.get_height() // 2 + 30))
        screen.blit(resume_surf, resume_rect)

        restart_text = f"R - {self.app.lang.get('race_respawn', 'Respawn')} | SHIFT+R - Restart"
        restart_surf = self.info_font.render(restart_text, True, TEXT_DIM)
        restart_rect = restart_surf.get_rect(center=(screen.get_width() // 2, screen.get_height() // 2 + 70))
        screen.blit(restart_surf, restart_rect)

        # Quit Instruction
        quit_text = f"ENTER - {self.app.lang.get('race_quit')}"
        quit_surf = self.info_font.render(quit_text, True, ACCENT_RED)
        quit_rect = quit_surf.get_rect(center=(screen.get_width() // 2, screen.get_height() // 2 + 110))
        screen.blit(quit_surf, quit_rect)

    def _draw_results(self, screen):
        """Draw the race results overlay."""
        # Semi-transparent overlay
        overlay = pg.Surface(screen.get_size(), pg.SRCALPHA)
        overlay.fill((0, 0, 0, 200))
        screen.blit(overlay, (0, 0))

        cx = screen.get_width() // 2
        cy = screen.get_height() // 2

        # Finished text
        finished_text = self.app.lang.get("race_finished")
        finished_surf = self.big_font.render(finished_text, True, TEXT_MAIN)
        screen.blit(finished_surf, finished_surf.get_rect(center=(cx, cy - 100)))

        # Win/Lose text
        if self.winner == 'PLAYER':
            result_text = self.app.lang.get("race_you_win")
            result_color = ACCENT_GREEN
        else:
            result_text = self.app.lang.get("race_you_lose")
            result_color = ACCENT_RED

        result_surf = self.big_font.render(result_text, True, result_color)
        screen.blit(result_surf, result_surf.get_rect(center=(cx, cy - 20)))

        # Reward
        if self.reward > 0:
            reward_text = f"{self.app.lang.get('race_reward_earned')}: ${self.reward}"
            reward_surf = self.hud_font.render(reward_text, True, ACCENT_GOLD)
            screen.blit(reward_surf, reward_surf.get_rect(center=(cx, cy + 60)))

        # Time
        minutes = int(self.race_time) // 60
        seconds = int(self.race_time) % 60
        time_text = f"{self.app.lang.get('race_time')}: {minutes:02d}:{seconds:02d}"
        time_surf = self.info_font.render(time_text, True, TEXT_DIM)
        screen.blit(time_surf, time_surf.get_rect(center=(cx, cy + 100)))

        # Continue instruction
        continue_text = f"ENTER - {self.app.lang.get('race_continue')}"
        continue_surf = self.info_font.render(continue_text, True, TEXT_DIM)
        screen.blit(continue_surf, continue_surf.get_rect(center=(cx, cy + 150)))

    def _draw_waypoints(self, screen):
        """Debug: Draw waypoints on the track."""
        # This is now handled by track_manager.draw_debug and ai_driver.draw_debug
        pass
