import pygame as pg
from src.constants import *
from src.game.player import Player
from src.game.race import Race
from src.ui.widgets import Button
from src.ui.menu_settings import PlayerSettingsMenu
from src.ui.menu_garage import GarageMenu
from src.ui.menu_shop import ShopMenu
from src.ui.menu_race_select import RaceSelectMenu
from src.core.assets import get_car_sprite
from src.core.game_data import get_car_display_name
from src.core.game_data import get_all_tracks, normalize_track_id


class GameSession:
    """
    Manages the active game session (Hub, Race, etc).
    """
    def __init__(self, app, slot_id):
        self.app = app
        self.slot_id = slot_id

        # Load Data
        self.game_db = app.data_manager.load_game_data()
        self.player_data = app.data_manager.load_player_state(slot_id)
        self.player = Player(self.player_data, self.game_db)

        self.state = 'HUB'
        self.buttons = []

        # Sub-menus
        self.player_menu = PlayerSettingsMenu(app, self.player, self.return_to_hub)
        self.garage_menu = GarageMenu(app, self.player, self.return_to_hub)
        self.shop_menu = ShopMenu(app, self.player, self.return_to_hub)
        self.race_select_menu = RaceSelectMenu(app, self.player, self.start_race, self.return_to_hub)

        # Active race instance
        self.current_race = None

        self.init_hub()
        print(f"[GAME] Started. Player: {self.player.name}")

    def init_hub(self):
        """Initializes buttons for the main hub view."""
        self.buttons = []
        cx = self.app.screen.get_width() // 2
        y, gap = 220, 75
        opts = [
            ("hub_race", lambda: self.set_state('RACE_SELECT'), ACCENT_GREEN),
            ("hub_garage", lambda: self.set_state('GARAGE'), None),
            ("hub_shop", lambda: self.set_state('SHOP'), None),
            ("hub_settings", lambda: self.set_state('PLAYER_SETTINGS'), None),
            ("hub_main_menu", self.exit, ACCENT_RED)
        ]
        for i, (k, act, col) in enumerate(opts):
            self.buttons.append(Button(self.app.lang.get(k), (cx, y+i*gap), act, app=self.app, custom_color=col))

    def set_state(self, st):
        """Switches sub-state within the session."""
        self.state = st
        if st == 'PLAYER_SETTINGS':
            self.player_menu.init_ui()
        elif st == 'GARAGE':
            self.garage_menu.init_ui()  # Re-calculate grid in case inventory changed
        elif st == 'SHOP':
            self.shop_menu.init_ui()  # Re-initialize shop UI
        elif st == 'RACE_SELECT':
            self.race_select_menu.init_ui()

    def return_to_hub(self):
        self.current_race = None
        self.set_state('HUB')
        # Save after returning from race (in case of rewards)
        self.app.data_manager.save_player_state(self.slot_id, self.player.to_dict())

    def start_race(self, track_id: str, opponent_id: str, difficulty: str):
        """Start a race with the selected track and opponent."""
        if not self.player.current_car:
            print("[SESSION] No car selected, race cannot start.")
            return
        track_id = normalize_track_id(track_id)
        if not self.player.current_car:
            print("[SESSION] No car selected, race cannot start.")
            return
        try:
            self.current_race = Race(
                self.app,
                self.player,
                track_id,
                self.return_to_hub,
                opponent_id,
                difficulty
            )
        except Exception as e:
            print(f"[SESSION] CRITICAL: Failed to initialize race. Error: {e}")
            import traceback
            traceback.print_exc()
            self.return_to_hub()
            return
        self.state = 'RACING'
        # Switch to race music
        self.app.audio.play_music('race_theme')

    def exit(self):
        """Saves and exits to main menu."""
        self.app.data_manager.save_player_state(self.slot_id, self.player.to_dict())
        self.app.close_session()

    def update(self, events, dt):
        """Central update delegation."""
        if self.state == 'HUB':
            for e in events:
                for b in self.buttons: b.handle_event(e)
        elif self.state == 'PLAYER_SETTINGS':
            for e in events: self.player_menu.update(e)
        elif self.state == 'GARAGE':
            for e in events: self.garage_menu.update(e)
        elif self.state == 'SHOP':
            for e in events: self.shop_menu.update(e)
        elif self.state == 'RACE_SELECT':
            for e in events: self.race_select_menu.update(e)
        elif self.state == 'RACING' and self.current_race:
            self.current_race.update(events, dt)
            return  # Don't process global ESC in racing mode

        # Global ESC to back/exit (not during active race)
        for e in events:
            if e.type == pg.KEYDOWN and e.key == pg.K_ESCAPE:
                if self.state not in ('HUB', 'RACING'):
                    self.return_to_hub()

    def draw(self):
        """Central draw delegation."""
        self.app.screen.fill(BG_COLOR)

        if self.state == 'HUB':
            self.draw_info()
            self.draw_hub()
        elif self.state == 'PLAYER_SETTINGS':
            self.player_menu.draw(self.app.screen)
        elif self.state == 'GARAGE':
            self.garage_menu.draw(self.app.screen)
        elif self.state == 'SHOP':
            self.shop_menu.draw(self.app.screen)
        elif self.state == 'RACE_SELECT':
            self.race_select_menu.draw(self.app.screen)
        elif self.state == 'RACING' and self.current_race:
            self.current_race.draw(self.app.screen)
        else:
            self.draw_placeholder(self.state)

    def draw_info(self):
        """Draws top status bar."""
        w = self.app.screen.get_width()
        pg.draw.rect(self.app.screen, PANEL_BG, (0,0,w,60))
        pg.draw.line(self.app.screen, ACCENT_BLUE, (0,60), (w,60), 2)
        f = pg.font.SysFont('Consolas', 24)
        
        ns = f.render(f"{self.app.lang.get('info_driver')}: {self.player.name}", True, TEXT_MAIN)
        self.app.screen.blit(ns, (20, 18))
        ms = f.render(f"${self.player.money}", True, ACCENT_GOLD)
        self.app.screen.blit(ms, ms.get_rect(topright=(w-20, 18)))

    def draw_hub(self):
        """Draws Hub specific elements."""
        for b in self.buttons: b.draw(self.app.screen)

        # Draw CURRENT CAR Preview on the left with background panel
        if self.player.current_car:
            sprite = get_car_sprite(self.app.assets, self.player.current_car)
            if sprite:
                # Background panel for car preview
                panel_rect = pg.Rect(80, 220, 200, 280)
                pg.draw.rect(self.app.screen, PANEL_BG, panel_rect, border_radius=12)
                pg.draw.rect(self.app.screen, TEXT_DIM, panel_rect, 2, border_radius=12)
                
                # Scale up for display maintaining 5:8 aspect ratio
                scaled = pg.transform.scale(sprite, (125, 200))
                # Center the car in the panel
                car_x = panel_rect.centerx - scaled.get_width() // 2
                car_y = panel_rect.y + 20
                self.app.screen.blit(scaled, (car_x, car_y))

                # Draw label "Current Ride"
                f = pg.font.SysFont('Consolas', 22, bold=True)
                lbl = f.render(self.app.lang.get("lbl_current_car"), True, ACCENT_GOLD)
                lbl_rect = lbl.get_rect(centerx=panel_rect.centerx, y=panel_rect.y + 230)
                self.app.screen.blit(lbl, lbl_rect)

                # Draw Car Name (display name from localization)
                display_name = get_car_display_name(self.player.current_car, self.app.lang)
                name_font = pg.font.SysFont('Consolas', 20, bold=True)
                name_s = name_font.render(display_name, True, TEXT_MAIN)
                name_rect = name_s.get_rect(centerx=panel_rect.centerx, y=panel_rect.y + 260)
                self.app.screen.blit(name_s, name_rect)

    def draw_placeholder(self, txt):
        s = pg.font.SysFont('Consolas',32,bold=True).render(txt,True,TEXT_DIM)
        self.app.screen.blit(s, s.get_rect(center=(self.app.screen.get_width()//2, self.app.screen.get_height()//2)))

    def cleanup(self):
        """Clean up resources when session ends."""
        if self.current_race:
            # Stop any race-related audio or resources
            if hasattr(self.current_race, 'cleanup'):
                self.current_race.cleanup()

        # Save player data
        if hasattr(self, 'player') and self.player:
            self.app.data_manager.save_player_state(self.slot_id, self.player.to_dict())